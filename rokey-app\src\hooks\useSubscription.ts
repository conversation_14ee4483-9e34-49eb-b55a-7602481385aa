import { useState, useEffect } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { SubscriptionStatus, UsageStatus, SubscriptionTier } from '@/lib/stripe-client';
import { User } from '@supabase/supabase-js';

export function useSubscription() {
  const [user, setUser] = useState<User | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [usageStatus, setUsageStatus] = useState<UsageStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createSupabaseBrowserClient();

  useEffect(() => {
    // Get initial user (more secure than getSession)
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user ?? null);

      if (user) {
        fetchSubscriptionStatus(user);
        fetchUsageStatus(user);
      } else {
        setLoading(false);
      }
    };

    getUser();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change in useSubscription:', event, !!session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Clear any cached data when switching users
          setSubscriptionStatus(null);
          setUsageStatus(null);
          setLoading(true);

          // Clear user-specific cache entries
          try {
            const { clearUserSpecificCache } = await import('@/utils/clearUserCache');
            await clearUserSpecificCache(session.user.id);
            console.log('User-specific cache cleared for new user:', session.user.id);
          } catch (cacheError) {
            console.warn('Could not clear user cache:', cacheError);
          }

          // Small delay to ensure cache clearing is complete
          await new Promise(resolve => setTimeout(resolve, 100));

          // Fetch fresh data for the new user
          await Promise.all([
            fetchSubscriptionStatus(session.user),
            fetchUsageStatus(session.user)
          ]);
        } else {
          // User signed out - clear all data
          setSubscriptionStatus(null);
          setUsageStatus(null);
          setError(null);
          setLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchSubscriptionStatus = async (currentUser: User) => {
    try {
      console.log('Fetching subscription status for user:', currentUser.id);

      // Force fresh data by adding cache-busting timestamp
      const timestamp = Date.now();
      const response = await fetch(`/api/stripe/subscription-status?userId=${currentUser.id}&_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Subscription status API error:', {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        throw new Error(`Failed to fetch subscription status: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Subscription status data:', data);
      setSubscriptionStatus(data);
    } catch (err) {
      console.error('Error fetching subscription status:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  const fetchUsageStatus = async (currentUser: User) => {
    try {
      // Force fresh data with cache-busting headers
      const response = await fetch('/api/stripe/subscription-status', {
        method: 'POST',
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        body: JSON.stringify({
          userId: currentUser.id,
          _t: Date.now() // Cache buster
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch usage status');
      }

      const data = await response.json();
      console.log('Usage status data:', data);
      setUsageStatus(data);
    } catch (err) {
      console.error('Error fetching usage status:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const createCheckoutSession = async (tier: SubscriptionTier) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch('/api/stripe/create-checkout-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        priceId: getPriceIdForTier(tier),
        userId: user.id,
        userEmail: user.email,
        tier,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create checkout session');
    }

    const { url } = await response.json();
    window.location.href = url;
  };

  const openCustomerPortal = async () => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch('/api/stripe/customer-portal', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: user.id,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to open customer portal');
    }

    const { url } = await response.json();
    window.location.href = url;
  };

  const refreshStatus = async () => {
    if (user) {
      console.log('Manual refresh triggered for user:', user.id);
      setLoading(true);

      // Clear cache before refreshing
      try {
        const { clearUserSpecificCache } = await import('@/utils/clearUserCache');
        await clearUserSpecificCache(user.id);
        console.log('Cache cleared before manual refresh');
      } catch (error) {
        console.warn('Failed to clear cache before refresh:', error);
      }

      // Small delay to ensure cache clearing is complete
      await new Promise(resolve => setTimeout(resolve, 100));

      await Promise.all([
        fetchSubscriptionStatus(user),
        fetchUsageStatus(user)
      ]);
    }
  };

  return {
    subscriptionStatus,
    usageStatus,
    loading,
    error,
    createCheckoutSession,
    openCustomerPortal,
    refreshStatus,
    isAuthenticated: !!user,
    user,
  };
}

function getPriceIdForTier(tier: SubscriptionTier): string {
  switch (tier) {
    case 'free':
      return process.env.NEXT_PUBLIC_STRIPE_FREE_PRICE_ID!;
    case 'starter':
      return process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID!;
    case 'professional':
      return process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID!;
    case 'enterprise':
      return process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID!;
    default:
      throw new Error(`Invalid tier: ${tier}`);
  }
}
