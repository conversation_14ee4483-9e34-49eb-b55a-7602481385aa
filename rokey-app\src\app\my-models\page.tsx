'use client';

import { useState, useEffect, FormEvent } from 'react';
import Link from 'next/link';
import { PlusCircleIcon, TrashIcon, PencilIcon, KeyIcon, CalendarIcon } from '@heroicons/react/24/outline';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useConfirmation } from '@/hooks/useConfirmation';
import Button from '@/components/ui/Button';
import Card, { CardHeader, CardContent } from '@/components/ui/Card';
import { LoadingCard } from '@/components/ui/LoadingSpinner';
import { useManageKeysPrefetch } from '@/hooks/useManageKeysPrefetch';
import { LimitIndicator, TierGuard } from '@/components/TierEnforcement';
import { useSubscription } from '@/hooks/useSubscription';

// Type for a custom API configuration (can be expanded later)
interface CustomApiConfig {
  id: string;
  name: string;
  created_at: string;
  // user_id?: string;
}

export default function MyModelsPage() {
  const [configs, setConfigs] = useState<CustomApiConfig[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [newConfigName, setNewConfigName] = useState<string>('');
  const [isCreating, setIsCreating] = useState<boolean>(false);

  // Confirmation modal hook
  const confirmation = useConfirmation();
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false);

  // Prefetch hook for manage keys pages
  const { createHoverPrefetch, prefetchManageKeysData } = useManageKeysPrefetch();

  // Subscription hook for tier limits and user authentication
  const { subscriptionStatus, user } = useSubscription();

  const fetchConfigs = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/custom-configs');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch configurations');
      }
      const data: CustomApiConfig[] = await response.json();
      setConfigs(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch configs when user is authenticated
    if (user) {
      fetchConfigs();
    } else if (user === null) {
      // User is explicitly null (not authenticated), stop loading
      setIsLoading(false);
    }
    // If user is undefined, we're still loading auth state, keep loading
  }, [user]); // Depend on user from useSubscription

  const handleCreateConfig = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!newConfigName.trim()) {
      setError('Configuration name cannot be empty.');
      return;
    }
    setIsCreating(true);
    setError(null);
    try {
      const response = await fetch('/api/custom-configs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newConfigName }),
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.details || result.error || 'Failed to create configuration');
      }
      setNewConfigName('');
      setShowCreateForm(false); // Hide form on success
      await fetchConfigs(); // Refresh the list
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsCreating(false);
    }
  };
  
  const handleDeleteConfig = (configId: string, configName: string) => {
    confirmation.showConfirmation(
      {
        title: 'Delete Configuration',
        message: `Are you sure you want to delete "${configName}"? This will permanently remove the configuration and all associated API keys. This action cannot be undone.`,
        confirmText: 'Delete Configuration',
        cancelText: 'Cancel',
        type: 'danger'
      },
      async () => {
        setError(null);
        try {
          const response = await fetch(`/api/custom-configs/${configId}`, {
            method: 'DELETE',
          });
          const result = await response.json();
          if (!response.ok) {
            throw new Error(result.details || result.error || 'Failed to delete configuration');
          }
          await fetchConfigs(); // Refresh the list
        } catch (err: any) {
          setError(`Failed to delete: ${err.message}`);
          throw err; // Re-throw to keep modal open on error
        }
      }
    );
  };

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-4xl font-bold text-gray-900">
            My API Models
          </h1>
          <p className="text-gray-600 mt-2">
            Manage your custom API configurations and keys
          </p>
        </div>
        <TierGuard
          feature="configurations"
          currentCount={configs.length}
          customMessage="You've reached your configuration limit. Upgrade to create more API configurations and organize your models better."
          fallback={
            <div className="flex flex-col items-end gap-2">
              <button
                disabled
                className="btn-primary opacity-50 cursor-not-allowed"
              >
                <PlusCircleIcon className="h-4 w-4 mr-2" />
                Create New Model
              </button>
              <p className="text-xs text-orange-600 font-medium">
                {subscriptionStatus?.tier === 'free'
                  ? 'Upgrade to Starter for more configurations'
                  : 'Configuration limit reached - upgrade for more'
                }
              </p>
            </div>
          }
        >
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className={showCreateForm ? "btn-secondary" : "btn-primary"}
          >
            <PlusCircleIcon className="h-4 w-4 mr-2" />
            {showCreateForm ? 'Cancel' : 'Create New Model'}
          </button>
        </TierGuard>
      </div>

      {/* Configuration Limits - Subtle inline indicator */}
      {subscriptionStatus && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-2">
          <LimitIndicator
            current={configs.length}
            limit={subscriptionStatus.tier === 'free' ? 1 :
                   subscriptionStatus.tier === 'starter' ? 4 :
                   subscriptionStatus.tier === 'professional' ? 20 : 999999}
            label="API Configurations"
            tier={subscriptionStatus.tier}
            showUpgradeHint={true}
          />
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="card border-red-200 bg-red-50 p-4">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Create Form */}
      {showCreateForm && (
        <div className="card max-w-md animate-scale-in p-6">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Create New Model</h3>
            <p className="text-gray-600">Set up a new API configuration</p>
          </div>
          <form onSubmit={handleCreateConfig} className="space-y-6">
            <div>
              <label htmlFor="configName" className="block text-sm font-medium text-gray-700 mb-2">
                Model Name
              </label>
              <input
                type="text"
                id="configName"
                value={newConfigName}
                onChange={(e) => setNewConfigName(e.target.value)}
                required
                className="form-input"
                placeholder="e.g., My Main Chat Assistant"
              />
            </div>
            <button
              type="submit"
              disabled={isCreating}
              className="btn-primary w-full"
            >
              {isCreating ? 'Creating...' : 'Create Model'}
            </button>
          </form>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <LoadingCard key={i} />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !configs.length && !error && !showCreateForm && (
        <div className="card text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100">
              <KeyIcon className="h-8 w-8 text-orange-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No API Models Yet</h3>
            <p className="text-gray-600 mb-6">
              Create your first API model configuration to get started with RoKey.
            </p>
            <TierGuard
              feature="configurations"
              currentCount={configs.length}
              customMessage="Create your first API configuration to get started with RouKey. Free tier includes 1 configuration."
              fallback={
                <div className="flex flex-col items-center gap-2">
                  <button
                    disabled
                    className="btn-primary opacity-50 cursor-not-allowed"
                  >
                    <PlusCircleIcon className="h-4 w-4 mr-2" />
                    Create Your First Model
                  </button>
                  <p className="text-xs text-orange-600 font-medium">
                    Upgrade to create configurations
                  </p>
                </div>
              }
            >
              <button
                onClick={() => setShowCreateForm(true)}
                className="btn-primary"
              >
                <PlusCircleIcon className="h-4 w-4 mr-2" />
                Create Your First Model
              </button>
            </TierGuard>
          </div>
        </div>
      )}

      {/* Models Grid */}
      {!isLoading && configs.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {configs.map((config, index) => (
            <div
              key={config.id}
              className="card p-6 hover:shadow-lg transition-all duration-200 animate-slide-in"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 truncate">
                    {config.name}
                  </h3>
                  <div className="space-y-1">
                    <div className="flex items-center text-xs text-gray-600">
                      <KeyIcon className="h-3 w-3 mr-1" />
                      ID: {config.id.slice(0, 8)}...
                    </div>
                    <div className="flex items-center text-xs text-gray-600">
                      <CalendarIcon className="h-3 w-3 mr-1" />
                      Created: {new Date(config.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center shrink-0 border border-orange-100">
                  <KeyIcon className="h-6 w-6 text-orange-600" />
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 mt-6">
                <Link
                  href={`/my-models/${config.id}`}
                  className="flex-1"
                  {...createHoverPrefetch(config.id)}
                >
                  <button className="btn-primary w-full">
                    <PencilIcon className="h-4 w-4 mr-2" />
                    Manage Keys
                  </button>
                </Link>
                <button
                  onClick={() => handleDeleteConfig(config.id, config.name)}
                  className="btn-secondary text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        type={confirmation.type}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}