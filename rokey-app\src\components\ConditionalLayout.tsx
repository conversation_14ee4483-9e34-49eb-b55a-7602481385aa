'use client';

import { usePathname } from 'next/navigation';
import { SidebarProvider } from "@/contexts/SidebarContext";
import { NavigationProvider } from "@/contexts/NavigationContext";
import LayoutContent from "@/components/LayoutContent";
import { useToast, ToastContainer } from "@/components/ui/Toast";

export default function ConditionalLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const { toasts, removeToast } = useToast();

  // Check if this is a marketing page (landing, pricing, auth, features, about, routing-strategies, contact, docs)
  const isMarketingPage = pathname === '/' ||
                          pathname.startsWith('/pricing') ||
                          pathname.startsWith('/features') ||
                          pathname.startsWith('/about') ||
                          pathname.startsWith('/routing-strategies') ||
                          pathname.startsWith('/contact') ||
                          pathname.startsWith('/docs') ||
                          pathname.startsWith('/auth/');

  // For marketing pages, render children directly without sidebar/navbar but with toast support
  if (isMarketingPage) {
    return (
      <>
        {children}
        <ToastContainer toasts={toasts} onRemove={removeToast} />
      </>
    );
  }

  // For app pages, use the full layout with sidebar and navbar
  return (
    <SidebarProvider>
      <NavigationProvider>
        <LayoutContent>
          {children}
        </LayoutContent>
      </NavigationProvider>
    </SidebarProvider>
  );
}
