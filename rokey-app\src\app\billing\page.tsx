'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import {
  CreditCardIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useSubscription } from '@/hooks/useSubscription';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useConfirmation } from '@/hooks/useConfirmation';
import emailjs from '@emailjs/browser';

interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface Plan {
  id: string;
  name: string;
  price: number;
  interval: string;
  features: PlanFeature[];
  popular?: boolean;
}

const plans: Plan[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    interval: 'forever',
    features: [
      { name: 'Strict fallback routing only', included: true },
      { name: 'Basic analytics', included: true },
      { name: 'No custom roles', included: false },
      { name: 'Configurations', included: true, limit: '1 max' },
      { name: 'API keys per config', included: true, limit: '3 max' },
      { name: 'User-generated API keys', included: true, limit: '3 max' }
    ]
  },
  {
    id: 'starter',
    name: 'Starter',
    price: 19,
    interval: 'month',
    popular: true,
    features: [
      { name: 'All routing strategies', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Custom roles', included: true, limit: 'Up to 3 roles' },
      { name: 'Configurations', included: true, limit: '5 max' },
      { name: 'API keys per config', included: true, limit: '15 max' },
      { name: 'User-generated API keys', included: true, limit: '50 max' },
      { name: 'Browsing tasks', included: true, limit: '15/month' }
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 49,
    interval: 'month',
    features: [
      { name: 'Everything in Starter', included: true },
      { name: 'Unlimited configurations', included: true },
      { name: 'Unlimited API keys per config', included: true },
      { name: 'Unlimited user-generated API keys', included: true },
      { name: 'Knowledge base documents', included: true, limit: '5 documents' },
      { name: 'Priority support', included: true }
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 149,
    interval: 'month',
    features: [
      { name: 'Everything in Professional', included: true },
      { name: 'Unlimited knowledge base documents', included: true },
      { name: 'Advanced semantic caching', included: true },
      { name: 'Custom integrations', included: true },
      { name: 'Dedicated support + phone', included: true },
      { name: 'SLA guarantee', included: true }
    ]
  }
];

export default function BillingPage() {
  const router = useRouter();
  const { user, subscriptionStatus, refreshStatus: refreshSubscription, createCheckoutSession } = useSubscription();
  const confirmation = useConfirmation();
  
  const [loading, setLoading] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [cancelFeedback, setCancelFeedback] = useState('');

  const currentPlan = plans.find(plan => plan.id === subscriptionStatus?.tier) || plans[0];

  // Calculate days until renewal based on actual subscription data
  const daysUntilRenewal = useMemo(() => {
    if (subscriptionStatus?.tier === 'free' || !subscriptionStatus?.currentPeriodEnd) {
      return null;
    }

    const renewalDate = new Date(subscriptionStatus.currentPeriodEnd);
    const today = new Date();
    const diffTime = renewalDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Return null if subscription has already expired
    return diffDays > 0 ? diffDays : null;
  }, [subscriptionStatus?.tier, subscriptionStatus?.currentPeriodEnd]);

  const handlePlanChange = async (planId: string) => {
    if (planId === currentPlan.id) return;

    const targetPlan = plans.find(p => p.id === planId);
    if (!targetPlan) return;

    const isUpgrade = plans.findIndex(p => p.id === planId) > plans.findIndex(p => p.id === currentPlan.id);

    if (isUpgrade) {
      // For upgrades, go directly to Stripe checkout
      try {
        setLoading(true);
        await createCheckoutSession(planId as any);
      } catch (error: any) {
        console.error('Checkout error:', error);
        toast.error('Failed to start checkout. Please try again.');
        setLoading(false);
      }
    } else {
      // For downgrades, show confirmation
      confirmation.showConfirmation(
        {
          title: 'Downgrade Plan',
          message: `Are you sure you want to downgrade to the ${targetPlan.name} plan? This will take effect at the end of your current billing period.`,
          confirmText: 'Downgrade',
          cancelText: 'Cancel',
          type: 'warning'
        },
        async () => {
          setLoading(true);
          try {
            // TODO: Implement downgrade logic with Stripe
            // For now, we'll simulate the process
            await new Promise(resolve => setTimeout(resolve, 2000));

            toast.success(`Successfully scheduled downgrade to ${targetPlan.name} plan`);
            await refreshSubscription();
          } catch (error: any) {
            console.error('Downgrade error:', error);
            toast.error('Failed to downgrade plan. Please try again.');
          } finally {
            setLoading(false);
          }
        }
      );
    }
  };

  const handleCancelSubscription = async () => {
    if (!cancelReason.trim()) {
      toast.error('Please select a reason for cancellation');
      return;
    }

    setLoading(true);
    try {
      // Send cancellation feedback via EmailJS
      const templateParams = {
        user_email: user?.email || 'Unknown',
        user_name: user?.user_metadata?.first_name || 'User',
        current_plan: currentPlan.name,
        cancel_reason: cancelReason,
        additional_feedback: cancelFeedback,
        cancel_date: new Date().toLocaleDateString()
      };

      await emailjs.send(
        process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID!,
        process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID!,
        templateParams,
        process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY!
      );

      // Here you would also cancel the subscription in your payment processor
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success('Subscription cancelled successfully. We\'ve sent your feedback to our team.');
      setShowCancelModal(false);
      setCancelReason('');
      setCancelFeedback('');
      await refreshSubscription();
    } catch (error: any) {
      console.error('Cancellation error:', error);
      toast.error('Failed to cancel subscription. Please contact support.');
    } finally {
      setLoading(false);
    }
  };

  const cancelReasons = [
    'Too expensive',
    'Not using enough features',
    'Found a better alternative',
    'Technical issues',
    'Poor customer support',
    'Missing features I need',
    'Temporary financial constraints',
    'Other'
  ];

  return (
    <div className="space-y-8">
      {/* Header with enhanced styling */}
      <div className="animate-slide-in">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">
          Billing & Plans 💳
        </h1>
        <p className="text-gray-600 text-lg">
          Manage your subscription, billing information, and plan features.
        </p>
      </div>

      {/* Current Plan Status - Enhanced Card */}
      <div className="card p-8 hover:shadow-lg transition-all duration-200 animate-slide-in">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-orange-100 rounded-lg">
            <CreditCardIcon className="h-6 w-6 text-orange-600" />
          </div>
          <h2 className="text-2xl font-semibold text-gray-900">Current Plan</h2>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-4 mb-3">
              <h3 className="text-3xl font-bold text-gray-900">{currentPlan.name}</h3>
              {currentPlan.popular && (
                <Badge className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-1 text-sm font-semibold">
                  ⭐ Popular
                </Badge>
              )}
            </div>
            <div className="space-y-2">
              <p className="text-xl font-semibold text-gray-700">
                {currentPlan.price === 0 ? (
                  <span className="text-green-600">Free forever</span>
                ) : (
                  <span>${currentPlan.price}<span className="text-gray-500 font-normal">/{currentPlan.interval}</span></span>
                )}
              </p>
              {daysUntilRenewal && (
                <div className="flex items-center gap-2 text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-lg inline-flex">
                  <CalendarIcon className="h-4 w-4" />
                  <span>Renews in {daysUntilRenewal} days</span>
                </div>
              )}
            </div>
          </div>
          <div className="text-right">
            {subscriptionStatus?.tier !== 'free' && (
              <Button
                variant="outline"
                onClick={() => setShowCancelModal(true)}
                className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 transition-all duration-200"
              >
                Cancel Subscription
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Available Plans - Enhanced Grid */}
      <div className="animate-slide-in" style={{ animationDelay: '200ms' }}>
        <div className="flex items-center gap-3 mb-8">
          <h2 className="text-3xl font-bold text-gray-900">Choose Your Plan</h2>
          <div className="h-px bg-gradient-to-r from-orange-200 to-transparent flex-1 ml-4"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {plans.map((plan, index) => (
            <div
              key={plan.id}
              className={`card relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-2 ${
                plan.popular
                  ? 'ring-2 ring-orange-500 shadow-lg scale-105'
                  : 'hover:shadow-lg'
              } ${plan.id === currentPlan.id ? 'bg-gradient-to-br from-orange-50 to-orange-100/50' : ''}`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                    🔥 Most Popular
                  </div>
                </div>
              )}

              {/* Current Plan Indicator */}
              {plan.id === currentPlan.id && (
                <div className="absolute top-4 right-4">
                  <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
                    <CheckCircleIcon className="h-3 w-3" />
                    Current
                  </div>
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">${plan.price}</span>
                    {plan.price > 0 && (
                      <span className="text-gray-500 text-lg">/{plan.interval}</span>
                    )}
                  </div>
                  {plan.price === 0 && (
                    <p className="text-green-600 font-semibold">Forever free</p>
                  )}
                </div>

                {/* Features List */}
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        {feature.included ? (
                          <div className="p-1 bg-green-100 rounded-full">
                            <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          </div>
                        ) : (
                          <div className="p-1 bg-gray-100 rounded-full">
                            <XCircleIcon className="h-4 w-4 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <span className={`text-sm leading-relaxed ${
                        feature.included ? 'text-gray-900' : 'text-gray-400'
                      }`}>
                        {feature.name}
                        {feature.limit && (
                          <span className="text-gray-500 font-medium"> ({feature.limit})</span>
                        )}
                      </span>
                    </li>
                  ))}
                </ul>

                {/* Action Button */}
                <div className="mt-auto">
                  {plan.id === currentPlan.id ? (
                    <Button disabled className="w-full bg-gray-100 text-gray-500 cursor-not-allowed">
                      <CheckCircleIcon className="h-4 w-4 mr-2" />
                      Current Plan
                    </Button>
                  ) : (
                    <Button
                      onClick={() => handlePlanChange(plan.id)}
                      disabled={loading}
                      className={`w-full transition-all duration-200 ${
                        plan.popular
                          ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl'
                          : ''
                      }`}
                      variant={plan.popular ? "default" : "outline"}
                    >
                      {plans.findIndex(p => p.id === plan.id) > plans.findIndex(p => p.id === currentPlan.id) ? (
                        <>
                          <ArrowUpIcon className="h-4 w-4 mr-2" />
                          Upgrade to {plan.name}
                        </>
                      ) : (
                        <>
                          <ArrowDownIcon className="h-4 w-4 mr-2" />
                          Downgrade to {plan.name}
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Additional Information Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 animate-slide-in" style={{ animationDelay: '400ms' }}>
        {/* Billing Information */}
        <div className="card p-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CreditCardIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Billing Information</h3>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center py-3 border-b border-gray-100">
              <span className="text-gray-600">Current Plan</span>
              <span className="font-semibold text-gray-900">{currentPlan.name}</span>
            </div>
            <div className="flex justify-between items-center py-3 border-b border-gray-100">
              <span className="text-gray-600">Billing Cycle</span>
              <span className="font-semibold text-gray-900">
                {currentPlan.price === 0 ? 'N/A' : `Monthly (${currentPlan.interval})`}
              </span>
            </div>
            {daysUntilRenewal && (
              <div className="flex justify-between items-center py-3 border-b border-gray-100">
                <span className="text-gray-600">Next Billing Date</span>
                <span className="font-semibold text-gray-900">
                  {new Date(Date.now() + daysUntilRenewal * 24 * 60 * 60 * 1000).toLocaleDateString()}
                </span>
              </div>
            )}
            <div className="flex justify-between items-center py-3">
              <span className="text-gray-600">Status</span>
              <Badge className={`${
                subscriptionStatus?.tier === 'free'
                  ? 'bg-gray-100 text-gray-800'
                  : 'bg-green-100 text-green-800'
              }`}>
                {subscriptionStatus?.tier === 'free' ? 'Free Plan' : 'Active'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Support & Help */}
        <div className="card p-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-purple-100 rounded-lg">
              <ExclamationTriangleIcon className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Need Help?</h3>
          </div>
          <div className="space-y-4">
            <p className="text-gray-600">
              Have questions about your subscription or need assistance with billing?
            </p>
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => window.open('/contact', '_blank')}
              >
                <EnvelopeIcon className="h-4 w-4 mr-2" />
                Contact Support
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Cancellation Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />
              <h3 className="text-lg font-semibold text-gray-900">Cancel Subscription</h3>
            </div>

            <p className="text-gray-600 mb-4">
              We're sorry to see you go! Please help us improve by telling us why you're cancelling.
            </p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for cancellation *
                </label>
                <select
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="">Select a reason...</option>
                  {cancelReasons.map((reason) => (
                    <option key={reason} value={reason}>{reason}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional feedback (optional)
                </label>
                <textarea
                  value={cancelFeedback}
                  onChange={(e) => setCancelFeedback(e.target.value)}
                  placeholder="Tell us more about your experience or what we could do better..."
                  rows={3}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowCancelModal(false)}
                className="flex-1"
              >
                Keep Subscription
              </Button>
              <Button
                onClick={handleCancelSubscription}
                disabled={loading || !cancelReason.trim()}
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                {loading ? 'Cancelling...' : 'Cancel Subscription'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        type={confirmation.type}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}
