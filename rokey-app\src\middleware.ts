import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  let res = NextResponse.next({
    request: {
      headers: req.headers,
    },
  });

  // Skip middleware for static files and Next.js internal routes
  const pathname = req.nextUrl.pathname;
  if (
    pathname.startsWith('/_next/static') ||
    pathname.startsWith('/_next/image') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/public/') ||
    pathname.includes('.')
  ) {
    return res;
  }

  // Temporary bypass for development if Supabase connectivity issues
  if (process.env.NODE_ENV === 'development' && process.env.BYPASS_AUTH_MIDDLEWARE === 'true') {
    console.log('Middleware: Bypassing auth checks due to BYPASS_AUTH_MIDDLEWARE=true');
    return res;
  }

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          req.cookies.set({
            name,
            value,
            ...options,
          });
          res = NextResponse.next({
            request: {
              headers: req.headers,
            },
          });
          res.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: any) {
          req.cookies.set({
            name,
            value: '',
            ...options,
          });
          res = NextResponse.next({
            request: {
              headers: req.headers,
            },
          });
          res.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );

  // Get the pathname (already defined above)

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/pricing',
    '/auth/signin',
    '/auth/signup',
    '/auth/callback',
    '/auth/verify-email',
    '/checkout', // Allow checkout for pending signups
    '/api/stripe/webhooks', // Stripe webhooks should be public
  ];

  // API routes that don't require authentication
  const publicApiRoutes = [
    '/api/stripe/webhooks',
    '/api/system-status',
    '/api/debug', // Debug endpoints
    '/api/pricing', // Pricing endpoints
    '/api/external', // External API endpoints (user-generated API keys)
  ];

  // Check if the route is public
  const isPublicRoute = publicRoutes.some(route => pathname === route || pathname.startsWith(route));
  const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route));

  // Allow public routes and API routes
  if (isPublicRoute || isPublicApiRoute) {
    return res;
  }

  // Get the user with error handling for network issues (more secure than getSession)
  let session = null;
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    // Create a session-like object for compatibility
    session = user ? { user } : null;
  } catch (error) {
    console.error('Middleware: Failed to get session from Supabase:', error);
    // If we can't connect to Supabase, allow the request to proceed
    // This prevents the entire app from being blocked by network issues
    return res;
  }

  // If no session and trying to access protected route, redirect to signin
  if (!session) {
    const redirectUrl = new URL('/auth/signin', req.url);
    redirectUrl.searchParams.set('redirectTo', pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // For authenticated users, check if they're accessing dashboard routes
  if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {
    // Check subscription status for protected app routes
    try {
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('subscription_status, subscription_tier')
        .eq('id', session.user.id)
        .single();

      if (profileError) {
        console.error('Middleware: Error fetching user profile:', profileError);
        // If we can't fetch profile due to network issues, allow access
        return res;
      }

      // If no profile exists, create a default free profile for the user
      if (!profile) {
        console.log('Middleware: No profile found for user, creating default free profile:', session.user.id);
        try {
          const { error: createError } = await supabase
            .from('user_profiles')
            .insert({
              id: session.user.id,
              full_name: session.user.user_metadata?.full_name || '',
              subscription_tier: 'free',
              subscription_status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            });

          if (createError) {
            console.error('Middleware: Error creating user profile:', createError);
            // If we can't create profile, redirect to pricing to be safe
            const redirectUrl = new URL('/pricing', req.url);
            redirectUrl.searchParams.set('checkout', 'true');
            redirectUrl.searchParams.set('message', 'profile_creation_failed');
            return NextResponse.redirect(redirectUrl);
          }

          // Profile created successfully, allow access
          console.log('Middleware: Successfully created free profile for user:', session.user.id);
          return res;
        } catch (error) {
          console.error('Middleware: Exception creating user profile:', error);
          const redirectUrl = new URL('/pricing', req.url);
          redirectUrl.searchParams.set('checkout', 'true');
          redirectUrl.searchParams.set('message', 'profile_creation_failed');
          return NextResponse.redirect(redirectUrl);
        }
      }

      // Check subscription status - free tier users should always have access
      // For free tier, we don't require subscription_status to be 'active' since they don't have paid subscriptions
      const hasActiveSubscription = profile.subscription_status === 'active' || profile.subscription_tier === 'free';

      if (!hasActiveSubscription) {
        const redirectUrl = new URL('/pricing', req.url);
        redirectUrl.searchParams.set('checkout', 'true');
        redirectUrl.searchParams.set('message', 'subscription_required');
        return NextResponse.redirect(redirectUrl);
      }
    } catch (error) {
      console.error('Error checking subscription status in middleware:', error);
      // On error, redirect to pricing to be safe
      const redirectUrl = new URL('/pricing', req.url);
      redirectUrl.searchParams.set('checkout', 'true');
      redirectUrl.searchParams.set('message', 'subscription_check_failed');
      return NextResponse.redirect(redirectUrl);
    }
  }

  return res;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
