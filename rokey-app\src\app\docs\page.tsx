'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentTextIcon,
  CodeBracketIcon,
  CogIcon,
  BoltIcon,
  KeyIcon,
  ChevronRightIcon,
  ClipboardDocumentIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SparklesIcon,
  ArrowTopRightOnSquareIcon,
  CircleStackIcon,
  ListBulletIcon,
  BookOpenIcon,
  PlayIcon,
  LightBulbIcon,
  CubeIcon,
  RocketLaunchIcon,
  QuestionMarkCircleIcon,
  ShieldCheckIcon,
  CpuChipIcon,
  UserGroupIcon,
  CloudIcon,
  ChartBarIcon,
  ArrowRightIcon,
  DocumentIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  ClockIcon,
  BellIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';

interface CodeBlockProps {
  children: string;
  language?: string;
  title?: string;
}

function CodeBlock({ children, language = 'javascript', title }: CodeBlockProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(children);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Enhanced syntax highlighting for different languages
  const highlightSyntax = (code: string, lang: string) => {
    const lines = code.split('\n');
    return lines.map((line, index) => {
      let highlightedLine = line;
      
      if (lang === 'javascript' || lang === 'typescript') {
        // Keywords
        highlightedLine = highlightedLine.replace(
          /\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new)\b/g,
          '<span class="text-purple-400">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
        // Comments
        highlightedLine = highlightedLine.replace(
          /(\/\/.*$)/g,
          '<span class="text-gray-500">$1</span>'
        );
        // Numbers
        highlightedLine = highlightedLine.replace(
          /\b(\d+\.?\d*)\b/g,
          '<span class="text-yellow-400">$1</span>'
        );
      } else if (lang === 'python') {
        // Python keywords
        highlightedLine = highlightedLine.replace(
          /\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False)\b/g,
          '<span class="text-purple-400">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
        // Comments
        highlightedLine = highlightedLine.replace(
          /(#.*$)/g,
          '<span class="text-gray-500">$1</span>'
        );
      } else if (lang === 'bash' || lang === 'shell') {
        // Bash commands
        highlightedLine = highlightedLine.replace(
          /\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed)\b/g,
          '<span class="text-blue-400">$1</span>'
        );
        // Flags
        highlightedLine = highlightedLine.replace(
          /(-[a-zA-Z]+)/g,
          '<span class="text-yellow-400">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
      } else if (lang === 'json') {
        // JSON keys
        highlightedLine = highlightedLine.replace(
          /"([^"]+)":/g,
          '<span class="text-blue-400">"$1"</span>:'
        );
        // JSON strings
        highlightedLine = highlightedLine.replace(
          /:\s*"([^"]*)"/g,
          ': <span class="text-green-400">"$1"</span>'
        );
        // JSON numbers
        highlightedLine = highlightedLine.replace(
          /:\s*(\d+\.?\d*)/g,
          ': <span class="text-yellow-400">$1</span>'
        );
        // JSON booleans
        highlightedLine = highlightedLine.replace(
          /:\s*(true|false|null)/g,
          ': <span class="text-purple-400">$1</span>'
        );
      }

      return (
        <div key={index} className="table-row">
          <span className="table-cell text-gray-500 text-right pr-4 select-none w-8">
            {index + 1}
          </span>
          <span 
            className="table-cell text-gray-100"
            dangerouslySetInnerHTML={{ __html: highlightedLine || ' ' }}
          />
        </div>
      );
    });
  };

  return (
    <div className="relative bg-gray-900 rounded-xl overflow-hidden border border-gray-300 shadow-lg">
      {title && (
        <div className="bg-gray-100 px-4 py-3 text-sm text-gray-700 border-b border-gray-300 flex items-center justify-between">
          <span className="font-medium">{title}</span>
          <span className="text-xs text-gray-500 uppercase tracking-wide">{language}</span>
        </div>
      )}
      <div className="relative">
        <pre className="p-4 overflow-x-auto text-sm font-mono leading-relaxed">
          <code className="table w-full">
            {highlightSyntax(children, language || 'javascript')}
          </code>
        </pre>
        <button
          onClick={handleCopy}
          className="absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-lg transition-all duration-200 border border-gray-300 shadow-sm"
          title="Copy to clipboard"
        >
          {copied ? (
            <CheckIcon className="h-4 w-4 text-green-600" />
          ) : (
            <ClipboardDocumentIcon className="h-4 w-4 text-gray-600" />
          )}
        </button>
      </div>
    </div>
  );
}

interface AlertProps {
  type: 'info' | 'warning' | 'tip';
  children: React.ReactNode;
}

function Alert({ type, children }: AlertProps) {
  const styles = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    tip: 'bg-green-50 border-green-200 text-green-800'
  };

  const iconStyles = {
    info: 'text-blue-600',
    warning: 'text-yellow-600',
    tip: 'text-green-600'
  };

  const icons = {
    info: InformationCircleIcon,
    warning: ExclamationTriangleIcon,
    tip: SparklesIcon
  };

  const Icon = icons[type];

  return (
    <div className={`border rounded-xl p-4 ${styles[type]}`}>
      <div className="flex items-start gap-3">
        <Icon className={`h-5 w-5 flex-shrink-0 mt-0.5 ${iconStyles[type]}`} />
        <div className="text-sm">{children}</div>
      </div>
    </div>
  );
}

export default function DocsPage() {
  const [activeSection, setActiveSection] = useState('overview');
  const [expandedSections, setExpandedSections] = useState<string[]>(['overview']);

  const sections = [
    {
      id: 'overview',
      title: 'Overview',
      icon: DocumentTextIcon,
      subsections: [
        { id: 'what-is-roukey', title: 'What is RouKey' },
        { id: 'key-benefits', title: 'Key Benefits' },
        { id: 'how-it-works', title: 'How It Works' },
        { id: 'architecture', title: 'Architecture' }
      ]
    },
    {
      id: 'getting-started',
      title: 'Getting Started',
      icon: PlayIcon,
      subsections: [
        { id: 'quickstart', title: 'Quickstart' },
        { id: 'installation', title: 'Installation' },
        { id: 'first-request', title: 'First Request' },
        { id: 'basic-setup', title: 'Basic Setup' }
      ]
    },
    {
      id: 'features',
      title: 'Features',
      icon: SparklesIcon,
      subsections: [
        { id: 'intelligent-routing', title: 'Intelligent Routing' },
        { id: 'multi-role-orchestration', title: 'Multi-Role Orchestration' },
        { id: 'semantic-caching', title: 'Semantic Caching' },
        { id: 'knowledge-base', title: 'Knowledge Base' },
        { id: 'custom-training', title: 'Custom Training' },
        { id: 'async-processing', title: 'Async Processing' },
        { id: 'analytics-monitoring', title: 'Analytics & Monitoring' }
      ]
    },
    {
      id: 'authentication',
      title: 'Authentication',
      icon: KeyIcon,
      subsections: [
        { id: 'api-keys', title: 'API Keys' },
        { id: 'auth-methods', title: 'Authentication Methods' },
        { id: 'security', title: 'Security' },
        { id: 'rate-limiting', title: 'Rate Limiting' }
      ]
    },
    {
      id: 'api-reference',
      title: 'API Reference',
      icon: CodeBracketIcon,
      subsections: [
        { id: 'base-url', title: 'Base URL' },
        { id: 'chat-completions', title: 'Chat Completions' },
        { id: 'streaming', title: 'Streaming' },
        { id: 'async-endpoints', title: 'Async Endpoints' },
        { id: 'parameters', title: 'Parameters' },
        { id: 'responses', title: 'Responses' },
        { id: 'errors', title: 'Error Handling' }
      ]
    },
    {
      id: 'routing-strategies',
      title: 'Routing Strategies',
      icon: BoltIcon,
      subsections: [
        { id: 'strategy-overview', title: 'Strategy Overview' },
        { id: 'intelligent-role-routing', title: 'Intelligent Role Routing' },
        { id: 'complexity-routing', title: 'Complexity-Based Routing' },
        { id: 'cost-optimized', title: 'Cost-Optimized Routing' },
        { id: 'strict-fallback', title: 'Strict Fallback' },
        { id: 'ab-testing', title: 'A/B Testing' },
        { id: 'custom-strategies', title: 'Custom Strategies' }
      ]
    },
    {
      id: 'configuration',
      title: 'Configuration',
      icon: CogIcon,
      subsections: [
        { id: 'dashboard-setup', title: 'Dashboard Setup' },
        { id: 'provider-keys', title: 'Provider API Keys' },
        { id: 'routing-config', title: 'Routing Configuration' },
        { id: 'custom-roles', title: 'Custom Roles' },
        { id: 'temperature-settings', title: 'Temperature Settings' }
      ]
    },
    {
      id: 'use-cases',
      title: 'Use Cases',
      icon: LightBulbIcon,
      subsections: [
        { id: 'development-coding', title: 'Development & Coding' },
        { id: 'content-creation', title: 'Content Creation' },
        { id: 'enterprise-apps', title: 'Enterprise Applications' },
        { id: 'educational-platforms', title: 'Educational Platforms' },
        { id: 'research-analysis', title: 'Research & Analysis' }
      ]
    },
    {
      id: 'examples',
      title: 'Examples',
      icon: CubeIcon,
      subsections: [
        { id: 'javascript-examples', title: 'JavaScript/Node.js' },
        { id: 'python-examples', title: 'Python' },
        { id: 'curl-examples', title: 'cURL' },
        { id: 'openai-sdk', title: 'OpenAI SDK Integration' }
      ]
    },
    {
      id: 'future-releases',
      title: 'Future Releases',
      icon: RocketLaunchIcon,
      subsections: [
        { id: 'q1-2025', title: 'Q1 2025 - Workflow Automation' },
        { id: 'q2-2025', title: 'Q2 2025 - Performance & Scale' },
        { id: 'q3-2025', title: 'Q3 2025 - AI-Powered Features' },
        { id: 'q4-2025', title: 'Q4 2025 - Enterprise Features' }
      ]
    },
    {
      id: 'faq',
      title: 'FAQ',
      icon: QuestionMarkCircleIcon,
      subsections: []
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      {/* Add top padding to account for fixed navbar */}
      <div className="pt-16 flex flex-col lg:flex-row min-h-screen">
        {/* Sidebar */}
        <div className="w-full lg:w-80 flex-shrink-0 bg-white border-r border-gray-200 shadow-sm lg:fixed lg:top-16 lg:left-0 lg:h-[calc(100vh-4rem)] lg:z-10 lg:overflow-y-auto">
          <div className="p-4 lg:p-6">
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">RouKey Documentation</h1>
              <p className="text-gray-600 text-sm">
                Complete guide to integrating and using RouKey's intelligent AI gateway
              </p>
            </div>

            <nav className="space-y-1">
              {sections.map((section) => {
                const Icon = section.icon;
                const isExpanded = expandedSections.includes(section.id);
                const hasSubsections = section.subsections && section.subsections.length > 0;

                return (
                  <div key={section.id}>
                    <button
                      onClick={() => {
                        setActiveSection(section.id);
                        if (hasSubsections) {
                          setExpandedSections(prev =>
                            isExpanded
                              ? prev.filter(id => id !== section.id)
                              : [...prev, section.id]
                          );
                        }
                      }}
                      className={`w-full flex items-center gap-3 px-4 py-3 text-sm rounded-xl transition-all duration-200 ${
                        activeSection === section.id
                          ? 'bg-[#ff6b35] text-white shadow-lg shadow-orange-500/25'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span className="flex-1 text-left">{section.title}</span>
                      {hasSubsections && (
                        <ChevronRightIcon
                          className={`h-4 w-4 transition-transform duration-200 ${
                            isExpanded ? 'rotate-90' : ''
                          }`}
                        />
                      )}
                    </button>

                    {hasSubsections && isExpanded && (
                      <div className="ml-8 mt-1 space-y-1">
                        {section.subsections.map((subsection) => (
                          <button
                            key={subsection.id}
                            onClick={() => setActiveSection(`${section.id}-${subsection.id}`)}
                            className={`w-full text-left px-3 py-2 text-xs rounded-lg transition-all duration-200 ${
                              activeSection === `${section.id}-${subsection.id}`
                                ? 'bg-[#ff6b35]/10 text-[#ff6b35] font-medium'
                                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                            }`}
                          >
                            {subsection.title}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
            </nav>

            <div className="mt-8 p-4 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
              <div className="flex items-center gap-2 mb-2">
                <SparklesIcon className="h-4 w-4 text-[#ff6b35]" />
                <span className="text-sm font-medium text-gray-900">Quick Start</span>
              </div>
              <p className="text-xs text-gray-600 mb-3">
                Get up and running in under 2 minutes
              </p>
              <button
                onClick={() => setActiveSection('overview')}
                className="w-full bg-[#ff6b35] hover:bg-[#e55a2b] text-white text-xs py-2 px-3 rounded-lg transition-colors"
              >
                Start Building
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white text-gray-900 min-h-screen lg:ml-80">
          <div className="max-w-4xl mx-auto p-4 lg:p-8 lg:py-12">
            <motion.div
              key={activeSection}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {activeSection === 'overview' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">
                      RouKey Overview
                    </h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey is a commercial BYOK (Bring Your Own Keys) framework that combines multiple AI routers and a gateway to optimize your LLM API usage through intelligent routing strategies.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div className="bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <DocumentTextIcon className="h-5 w-5 text-[#ff6b35]" />
                        What is RouKey?
                      </h3>
                      <p className="text-gray-600 mb-4">Learn about RouKey's core concepts and capabilities</p>
                      <button
                        onClick={() => setActiveSection('overview-what-is-roukey')}
                        className="text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors"
                      >
                        Learn more <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 p-6 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <SparklesIcon className="h-5 w-5 text-blue-600" />
                        Key Benefits
                      </h3>
                      <p className="text-gray-600 mb-4">Discover how RouKey optimizes your AI operations</p>
                      <button
                        onClick={() => setActiveSection('overview-key-benefits')}
                        className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Explore benefits <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-green-100/50 p-6 rounded-xl border border-green-200 hover:shadow-lg hover:border-green-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CogIcon className="h-5 w-5 text-green-600" />
                        How It Works
                      </h3>
                      <p className="text-gray-600 mb-4">Understand RouKey's intelligent routing process</p>
                      <button
                        onClick={() => setActiveSection('overview-how-it-works')}
                        className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        See how <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 p-6 rounded-xl border border-purple-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CircleStackIcon className="h-5 w-5 text-purple-600" />
                        Architecture
                      </h3>
                      <p className="text-gray-600 mb-4">Deep dive into RouKey's technical architecture</p>
                      <button
                        onClick={() => setActiveSection('overview-architecture')}
                        className="text-purple-600 hover:text-purple-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        View architecture <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">How RouKey Works</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-lg">1</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Request Analysis</h3>
                        <p className="text-gray-600 text-sm">RouKey analyzes incoming requests using AI classification to understand complexity, role requirements, and optimal routing strategy.</p>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <div className="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-lg">2</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Intelligent Routing</h3>
                        <p className="text-gray-600 text-sm">Based on your configured strategy, RouKey routes the request to the optimal model considering cost, performance, and availability.</p>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-lg">3</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Response Delivery</h3>
                        <p className="text-gray-600 text-sm">RouKey delivers the response with automatic failover, retry logic, and comprehensive analytics for monitoring and optimization.</p>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Key Benefits</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                          <SparklesIcon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg mb-2">Cost Optimization</h3>
                          <p className="text-gray-600">Reduce API costs by up to 60% through intelligent routing to cost-effective models for appropriate tasks.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                          <BoltIcon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg mb-2">Enhanced Reliability</h3>
                          <p className="text-gray-600">Automatic failover and retry mechanisms ensure 99.9% uptime with seamless provider switching.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                          <CogIcon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg mb-2">Easy Integration</h3>
                          <p className="text-gray-600">Drop-in replacement for OpenAI API with full compatibility and additional routing capabilities.</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                          <KeyIcon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-lg mb-2">Enterprise Security</h3>
                          <p className="text-gray-600">Your API keys stay secure with enterprise-grade encryption and never leave your control.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white">
                    <h3 className="text-xl font-semibold mb-4">Ready to optimize your AI costs?</h3>
                    <p className="text-white/90 mb-6">
                      Join thousands of developers who have reduced their AI API costs by up to 60% with RouKey's intelligent routing.
                    </p>
                    <button
                      onClick={() => setActiveSection('getting-started')}
                      className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                    >
                      Get Started Now
                    </button>
                  </div>
                </div>
              )}

              {/* Overview Subsections */}
              {activeSection === 'overview-what-is-roukey' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">What is RouKey?</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey is an intelligent AI gateway that revolutionizes how you interact with multiple LLM providers.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Core Concept</h2>
                    <div className="prose prose-gray max-w-none space-y-6">
                      <p className="text-gray-600 leading-relaxed text-lg">
                        RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers (OpenAI, Anthropic, Google, DeepSeek, xAI, and more).
                        It automatically routes requests to the most appropriate model based on your configured strategies, providing cost optimization, improved reliability, and enhanced performance.
                      </p>
                      <p className="text-gray-600 leading-relaxed text-lg">
                        Unlike traditional API proxies, RouKey uses advanced AI classification to understand your requests and make intelligent routing decisions.
                        This means you get the right model for the right task, every time.
                      </p>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">BYOK Framework</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey follows a Bring Your Own Keys (BYOK) model, ensuring you maintain complete control over your API keys and costs.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <KeyIcon className="h-5 w-5 text-green-600" />
                            Your Keys, Your Control
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• You provide your own API keys</li>
                            <li>• Direct billing from providers</li>
                            <li>• No markup on API costs</li>
                            <li>• Full transparency</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                            Enterprise Security
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• AES-256 encryption</li>
                            <li>• Keys never leave your control</li>
                            <li>• No response logging</li>
                            <li>• SOC 2 compliance ready</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Supported Providers</h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">OpenAI</div>
                        <div className="text-xs text-gray-600">GPT-4, GPT-3.5, o1</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Anthropic</div>
                        <div className="text-xs text-gray-600">Claude 3.5, Claude 3</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Google</div>
                        <div className="text-xs text-gray-600">Gemini Pro, Flash</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">DeepSeek</div>
                        <div className="text-xs text-gray-600">V2.5, Coder</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">xAI</div>
                        <div className="text-xs text-gray-600">Grok Models</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">OpenRouter</div>
                        <div className="text-xs text-gray-600">300+ Models</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Groq</div>
                        <div className="text-xs text-gray-600">Ultra-fast inference</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Together AI</div>
                        <div className="text-xs text-gray-600">Open source models</div>
                      </div>
                    </div>
                    <p className="text-gray-600 text-center">
                      Access to <strong>300+ AI models</strong> from leading providers with unified API interface
                    </p>
                  </div>
                </div>
              )}

              {activeSection === 'overview-key-benefits' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Key Benefits</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Discover how RouKey transforms your AI operations with intelligent routing and cost optimization.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="card p-8">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center">
                          <SparklesIcon className="h-6 w-6 text-white" />
                        </div>
                        <h2 className="text-2xl font-bold text-gray-900">Cost Optimization</h2>
                      </div>
                      <div className="space-y-4">
                        <p className="text-gray-600 leading-relaxed">
                          Reduce API costs by up to 60% through intelligent routing to cost-effective models for appropriate tasks.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Complexity-based routing to cheaper models for simple tasks</li>
                          <li>• Semantic caching prevents duplicate API calls</li>
                          <li>• Real-time cost tracking and optimization</li>
                          <li>• No markup on provider costs</li>
                        </ul>
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <div className="text-green-800 font-medium mb-1">Average Savings</div>
                          <div className="text-2xl font-bold text-green-600">40-60%</div>
                          <div className="text-green-600 text-sm">on monthly API costs</div>
                        </div>
                      </div>
                    </div>

                    <div className="card p-8">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
                          <BoltIcon className="h-6 w-6 text-white" />
                        </div>
                        <h2 className="text-2xl font-bold text-gray-900">Enhanced Reliability</h2>
                      </div>
                      <div className="space-y-4">
                        <p className="text-gray-600 leading-relaxed">
                          Automatic failover and retry mechanisms ensure 99.9% uptime with seamless provider switching.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Automatic failover between providers</li>
                          <li>• Intelligent retry logic with exponential backoff</li>
                          <li>• Real-time health monitoring</li>
                          <li>• Circuit breaker patterns</li>
                        </ul>
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <div className="text-blue-800 font-medium mb-1">Uptime SLA</div>
                          <div className="text-2xl font-bold text-blue-600">99.9%</div>
                          <div className="text-blue-600 text-sm">guaranteed availability</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'overview-how-it-works' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">How RouKey Works</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Understanding RouKey's intelligent routing process and decision-making algorithms.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Request Flow</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-xl">1</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-3">Request Analysis</h3>
                        <p className="text-gray-600 text-sm mb-4">RouKey analyzes incoming requests using AI classification to understand complexity, role requirements, and optimal routing strategy.</p>
                        <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">~50ms processing</div>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <div className="w-16 h-16 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-xl">2</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-3">Intelligent Routing</h3>
                        <p className="text-gray-600 text-sm mb-4">Based on your configured strategy, RouKey routes the request to the optimal model considering cost, performance, and availability.</p>
                        <div className="text-xs text-[#ff6b35] bg-[#ff6b35]/10 px-2 py-1 rounded">Real-time decisions</div>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <div className="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-xl">3</span>
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-3">Response Delivery</h3>
                        <p className="text-gray-600 text-sm mb-4">RouKey delivers the response with automatic failover, retry logic, and comprehensive analytics for monitoring and optimization.</p>
                        <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">Sub-second latency</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">AI Classification Engine</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey uses advanced Gemini-powered classification to understand your requests and make optimal routing decisions.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CpuChipIcon className="h-5 w-5 text-purple-600" />
                            Complexity Analysis
                          </h3>
                          <p className="text-gray-600 mb-4">Analyzes prompt complexity on a 1-5 scale to determine the most cost-effective model.</p>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Simple (1-2)</span>
                              <span className="text-green-600">GPT-3.5, Gemini Flash</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Complex (4-5)</span>
                              <span className="text-blue-600">GPT-4, Claude 3.5</span>
                            </div>
                          </div>
                        </div>
                        <div className="p-6 bg-gradient-to-br from-indigo-50 to-indigo-100/50 rounded-xl border border-indigo-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <UserGroupIcon className="h-5 w-5 text-indigo-600" />
                            Role Detection
                          </h3>
                          <p className="text-gray-600 mb-4">Identifies the type of task to route to specialized models.</p>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Coding</span>
                              <span className="text-green-600">DeepSeek Coder</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Writing</span>
                              <span className="text-blue-600">Claude 3.5</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'overview-architecture' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">RouKey Architecture</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Deep dive into RouKey's technical architecture and infrastructure design.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">System Architecture</h2>
                    <div className="space-y-6">
                      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                        <div className="text-center mb-6">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">RouKey Gateway Architecture</h3>
                          <p className="text-gray-600">High-level overview of RouKey's distributed system</p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-center">
                          <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
                            <div className="text-sm font-medium text-gray-900 mb-1">Client App</div>
                            <div className="text-xs text-gray-600">Your Application</div>
                          </div>
                          <div className="text-center">
                            <ChevronRightIcon className="h-6 w-6 text-gray-400 mx-auto" />
                          </div>
                          <div className="text-center p-4 bg-[#ff6b35]/10 rounded-lg border border-[#ff6b35]/20">
                            <div className="text-sm font-medium text-gray-900 mb-1">RouKey Gateway</div>
                            <div className="text-xs text-gray-600">Intelligent Router</div>
                          </div>
                          <div className="text-center">
                            <ChevronRightIcon className="h-6 w-6 text-gray-400 mx-auto" />
                          </div>
                          <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
                            <div className="text-sm font-medium text-gray-900 mb-1">LLM Providers</div>
                            <div className="text-xs text-gray-600">OpenAI, Anthropic, etc.</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="card p-8">
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">Core Components</h2>
                      <div className="space-y-4">
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <CpuChipIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Classification Engine</h3>
                            <p className="text-gray-600 text-sm">Gemini-powered request analysis and routing decisions</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <CircleStackIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Semantic Cache</h3>
                            <p className="text-gray-600 text-sm">Jina embeddings with reranking for intelligent caching</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <BoltIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">LangGraph Orchestration</h3>
                            <p className="text-gray-600 text-sm">Multi-role workflow management and coordination</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <ChartBarIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Analytics Engine</h3>
                            <p className="text-gray-600 text-sm">Real-time monitoring and performance optimization</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="card p-8">
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">Infrastructure</h2>
                      <div className="space-y-4">
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <CloudIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Vercel Edge Network</h3>
                            <p className="text-gray-600 text-sm">Global edge deployment for minimal latency</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-teal-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <CircleStackIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Supabase Database</h3>
                            <p className="text-gray-600 text-sm">PostgreSQL with real-time subscriptions</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <ShieldCheckIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Security Layer</h3>
                            <p className="text-gray-600 text-sm">AES-256 encryption and secure key management</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                            <BoltIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">Load Balancing</h3>
                            <p className="text-gray-600 text-sm">Intelligent request distribution and failover</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'getting-started' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">
                      Getting Started with RouKey
                    </h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Get up and running with RouKey in under 2 minutes. Follow our step-by-step guides to integrate RouKey into your application.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div className="bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <BoltIcon className="h-5 w-5 text-[#ff6b35]" />
                        Quickstart
                      </h3>
                      <p className="text-gray-600 mb-4">Get RouKey running in 2 minutes with our fastest setup guide</p>
                      <button
                        onClick={() => setActiveSection('getting-started-quickstart')}
                        className="text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors"
                      >
                        Start now <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 p-6 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CogIcon className="h-5 w-5 text-blue-600" />
                        Installation
                      </h3>
                      <p className="text-gray-600 mb-4">Detailed installation and setup instructions</p>
                      <button
                        onClick={() => setActiveSection('getting-started-installation')}
                        className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        View guide <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-green-100/50 p-6 rounded-xl border border-green-200 hover:shadow-lg hover:border-green-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <PlayIcon className="h-5 w-5 text-green-600" />
                        First Request
                      </h3>
                      <p className="text-gray-600 mb-4">Make your first API call and see RouKey in action</p>
                      <button
                        onClick={() => setActiveSection('getting-started-first-request')}
                        className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Try it out <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 p-6 rounded-xl border border-purple-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CogIcon className="h-5 w-5 text-purple-600" />
                        Basic Setup
                      </h3>
                      <p className="text-gray-600 mb-4">Configure routing strategies and optimize your setup</p>
                      <button
                        onClick={() => setActiveSection('getting-started-basic-setup')}
                        className="text-purple-600 hover:text-purple-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Configure <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white">
                    <h3 className="text-xl font-semibold mb-4">Need help getting started?</h3>
                    <p className="text-white/90 mb-6">
                      Our quickstart guide will have you up and running in under 2 minutes with your first RouKey API call.
                    </p>
                    <button
                      onClick={() => setActiveSection('getting-started-quickstart')}
                      className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                    >
                      Start Quickstart Guide
                    </button>
                  </div>
                </div>
              )}

              {/* Getting Started Subsections */}
              {activeSection === 'getting-started-quickstart' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Quickstart Guide</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Get RouKey up and running in under 2 minutes with this step-by-step guide.
                    </p>
                  </div>

                  <div className="bg-blue-50 p-6 rounded-xl border border-blue-200 mb-8">
                    <div className="flex items-center gap-3 mb-3">
                      <InformationCircleIcon className="h-6 w-6 text-blue-600" />
                      <h3 className="text-lg font-semibold text-blue-900">Prerequisites</h3>
                    </div>
                    <ul className="space-y-2 text-blue-800">
                      <li>• API keys from at least one LLM provider (OpenAI, Anthropic, etc.)</li>
                      <li>• A RouKey account (sign up at <a href="https://roukey.online" className="underline">roukey.online</a>)</li>
                      <li>• Basic knowledge of REST APIs</li>
                    </ul>
                  </div>

                  <div className="space-y-6">
                    <div className="card p-8">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold">1</div>
                        <h2 className="text-2xl font-bold text-gray-900">Create Your Account</h2>
                      </div>
                      <p className="text-gray-600 mb-4">
                        Sign up for a free RouKey account to get started with intelligent AI routing.
                      </p>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-sm text-gray-600 mb-2">Visit:</p>
                        <a href="https://roukey.online" className="text-[#ff6b35] hover:text-[#e55a2b] font-medium underline">
                          https://roukey.online
                        </a>
                      </div>
                    </div>

                    <div className="card p-8">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold">2</div>
                        <h2 className="text-2xl font-bold text-gray-900">Add Provider API Keys</h2>
                      </div>
                      <p className="text-gray-600 mb-4">
                        Add your LLM provider API keys to enable RouKey's intelligent routing.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2">Supported Providers:</h4>
                          <ul className="text-sm text-gray-600 space-y-1">
                            <li>• OpenAI (GPT-4, GPT-3.5)</li>
                            <li>• Anthropic (Claude 3.5)</li>
                            <li>• Google (Gemini Pro)</li>
                            <li>• DeepSeek (V2.5, Coder)</li>
                          </ul>
                        </div>
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <h4 className="font-medium text-gray-900 mb-2">Security:</h4>
                          <p className="text-sm text-gray-600">
                            Your keys are encrypted with AES-256 and never leave your control.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="card p-8">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold">3</div>
                        <h2 className="text-2xl font-bold text-gray-900">Generate Your RouKey API Key</h2>
                      </div>
                      <p className="text-gray-600 mb-4">
                        Create a user API key to access RouKey's intelligent routing from your application.
                      </p>
                      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                        <p className="text-sm text-yellow-800">
                          <strong>Important:</strong> Your API key will be displayed only once. Make sure to copy and store it securely.
                        </p>
                      </div>
                    </div>

                    <div className="card p-8">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center text-white font-bold">4</div>
                        <h2 className="text-2xl font-bold text-gray-900">Make Your First Request</h2>
                      </div>
                      <p className="text-gray-600 mb-6">
                        Test your setup with a simple API call to RouKey's chat completions endpoint.
                      </p>
                      <CodeBlock title="Your first RouKey API call" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Hello RouKey! How does intelligent routing work?"}
    ],
    "stream": false
  }'`}
                      </CodeBlock>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-500 to-green-600 p-8 rounded-xl text-white">
                    <h3 className="text-xl font-semibold mb-4">🎉 Congratulations!</h3>
                    <p className="text-white/90 mb-6">
                      You've successfully set up RouKey and made your first API call. RouKey is now intelligently routing your requests to optimize cost and performance.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <button
                        onClick={() => setActiveSection('getting-started-basic-setup')}
                        className="bg-white text-green-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                      >
                        Configure Routing Strategies
                      </button>
                      <button
                        onClick={() => setActiveSection('api-reference')}
                        className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors"
                      >
                        Explore API Reference
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'getting-started-installation' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Installation Guide</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Detailed installation and setup instructions for integrating RouKey into your application.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">SDK Installation</h2>
                    <p className="text-gray-600 mb-6">
                      RouKey is compatible with existing OpenAI SDKs. Simply change the base URL to start using RouKey's intelligent routing.
                    </p>

                    <div className="space-y-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">JavaScript/Node.js</h3>
                        <CodeBlock title="Install OpenAI SDK" language="bash">
{`npm install openai`}
                        </CodeBlock>
                        <div className="mt-4">
                          <CodeBlock title="Configure for RouKey" language="javascript">
{`import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: 'rk_live_your_api_key_here',
  baseURL: 'https://roukey.online/api/external/v1',
  defaultHeaders: {
    'X-API-Key': 'rk_live_your_api_key_here'
  }
});

// Use exactly like OpenAI
const completion = await openai.chat.completions.create({
  messages: [{ role: 'user', content: 'Hello RouKey!' }],
  model: 'gpt-4', // RouKey will intelligently route this
});`}
                          </CodeBlock>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Python</h3>
                        <CodeBlock title="Install OpenAI SDK" language="bash">
{`pip install openai`}
                        </CodeBlock>
                        <div className="mt-4">
                          <CodeBlock title="Configure for RouKey" language="python">
{`from openai import OpenAI

client = OpenAI(
    api_key="rk_live_your_api_key_here",
    base_url="https://roukey.online/api/external/v1",
    default_headers={"X-API-Key": "rk_live_your_api_key_here"}
)

# Use exactly like OpenAI
completion = client.chat.completions.create(
    messages=[{"role": "user", "content": "Hello RouKey!"}],
    model="gpt-4"  # RouKey will intelligently route this
)`}
                          </CodeBlock>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'getting-started-first-request' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Your First Request</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Learn how to make your first API call to RouKey and understand the response format.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Basic Request</h2>
                    <p className="text-gray-600 mb-6">
                      Here's a simple example of making your first request to RouKey's chat completions endpoint:
                    </p>
                    <CodeBlock title="Basic chat completion request" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Explain quantum computing in simple terms"}
    ],
    "stream": false,
    "temperature": 0.7
  }'`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Understanding the Response</h2>
                    <p className="text-gray-600 mb-6">
                      RouKey returns OpenAI-compatible responses with additional metadata about routing decisions:
                    </p>
                    <CodeBlock title="Example response" language="json">
{`{
  "id": "chatcmpl-abc123",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Quantum computing is like having a super-powered calculator..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 12,
    "completion_tokens": 150,
    "total_tokens": 162
  },
  "roukey_metadata": {
    "routing_strategy": "intelligent_role",
    "selected_provider": "openai",
    "complexity_score": 3,
    "estimated_cost": 0.0024
  }
}`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Request with Role-Based Routing</h2>
                    <p className="text-gray-600 mb-6">
                      Use RouKey's role parameter to optimize routing for specific tasks:
                    </p>
                    <CodeBlock title="Role-based routing request" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}
    ],
    "role": "coding",
    "stream": true,
    "max_tokens": 1000
  }'`}
                    </CodeBlock>
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mt-4">
                      <p className="text-blue-800 text-sm">
                        <strong>Tip:</strong> Using <code>role: "coding"</code> will route your request to models optimized for code generation like DeepSeek Coder.
                      </p>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Streaming Responses</h2>
                    <p className="text-gray-600 mb-6">
                      For better user experience and to avoid timeouts, use streaming responses:
                    </p>
                    <CodeBlock title="JavaScript streaming example" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [{ role: 'user', content: 'Write a story about AI' }],
    stream: true,
    role: 'writing'
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  const chunk = decoder.decode(value);
  const lines = chunk.split('\\n');

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') return;

      try {
        const parsed = JSON.parse(data);
        const content = parsed.choices[0]?.delta?.content;
        if (content) {
          console.log(content); // Stream the content
        }
      } catch (e) {
        // Handle parsing errors
      }
    }
  }
}`}
                    </CodeBlock>
                  </div>
                </div>
              )}

              {activeSection === 'getting-started-basic-setup' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Basic Setup</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Configure RouKey's routing strategies and optimize your setup for your specific use case.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Choosing a Routing Strategy</h2>
                    <p className="text-gray-600 mb-6">
                      RouKey offers several routing strategies. Choose the one that best fits your use case:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <BoltIcon className="h-5 w-5 text-blue-600" />
                          Intelligent Role Routing
                        </h3>
                        <p className="text-gray-600 mb-4">AI-powered classification routes requests based on detected roles (coding, writing, analysis, etc.) to specialized models.</p>
                        <div className="text-sm text-blue-600 font-medium">Best for: Multi-purpose applications</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <CircleStackIcon className="h-5 w-5 text-green-600" />
                          Complexity-Based Routing
                        </h3>
                        <p className="text-gray-600 mb-4">Analyzes prompt complexity (1-5 scale) and routes to appropriate models for optimal cost-performance balance.</p>
                        <div className="text-sm text-green-600 font-medium">Best for: Cost optimization</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <ListBulletIcon className="h-5 w-5 text-[#ff6b35]" />
                          Strict Fallback Strategy
                        </h3>
                        <p className="text-gray-600 mb-4">Ordered failover sequence with predictable routing and guaranteed fallback chain for maximum reliability.</p>
                        <div className="text-sm text-[#ff6b35] font-medium">Best for: Mission-critical applications</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <SparklesIcon className="h-5 w-5 text-purple-600" />
                          Cost-Optimized Routing
                        </h3>
                        <p className="text-gray-600 mb-4">Smart cost optimization with learning algorithms that adapt to your usage patterns over time.</p>
                        <div className="text-sm text-purple-600 font-medium">Best for: Budget-conscious deployments</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Custom Roles Configuration</h2>
                    <p className="text-gray-600 mb-6">
                      Create custom roles to optimize routing for your specific use cases:
                    </p>
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">Example: Custom "Data Analysis" Role</h4>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Primary: Claude 3.5 Sonnet (excellent for analysis)</li>
                          <li>• Fallback: GPT-4 (reliable backup)</li>
                          <li>• Cost-effective: Gemini Pro (for simple queries)</li>
                        </ul>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">Example: Custom "Code Review" Role</h4>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Primary: DeepSeek Coder (specialized for code)</li>
                          <li>• Fallback: GPT-4 (general coding capability)</li>
                          <li>• Fast: GPT-3.5 Turbo (for simple reviews)</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white">
                    <h3 className="text-xl font-semibold mb-4">Ready to optimize further?</h3>
                    <p className="text-white/90 mb-6">
                      Explore advanced features like semantic caching, knowledge base integration, and multi-role orchestration.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <button
                        onClick={() => setActiveSection('features')}
                        className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                      >
                        Explore Features
                      </button>
                      <button
                        onClick={() => setActiveSection('routing-strategies')}
                        className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors"
                      >
                        Advanced Routing
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'features' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">
                      RouKey Features
                    </h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Discover the comprehensive features that make RouKey the leading AI gateway solution for developers and enterprises.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 p-6 rounded-xl border border-blue-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <BoltIcon className="h-5 w-5 text-blue-600" />
                        Intelligent Routing
                      </h3>
                      <p className="text-gray-600 mb-4">AI-powered request classification and optimal model selection</p>
                      <button
                        onClick={() => setActiveSection('features-intelligent-routing')}
                        className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Learn more <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 p-6 rounded-xl border border-purple-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <UserGroupIcon className="h-5 w-5 text-purple-600" />
                        Multi-Role Orchestration
                      </h3>
                      <p className="text-gray-600 mb-4">Advanced workflow management with LangGraph integration</p>
                      <button
                        onClick={() => setActiveSection('features-multi-role-orchestration')}
                        className="text-purple-600 hover:text-purple-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Explore workflows <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-green-100/50 p-6 rounded-xl border border-green-200 hover:shadow-lg hover:border-green-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CircleStackIcon className="h-5 w-5 text-green-600" />
                        Semantic Caching
                      </h3>
                      <p className="text-gray-600 mb-4">Intelligent caching with embeddings and reranking</p>
                      <button
                        onClick={() => setActiveSection('features-semantic-caching')}
                        className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        See caching <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20 hover:shadow-lg hover:border-[#ff6b35]/30 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <BookOpenIcon className="h-5 w-5 text-[#ff6b35]" />
                        Knowledge Base
                      </h3>
                      <p className="text-gray-600 mb-4">Upload documents for enhanced AI responses</p>
                      <button
                        onClick={() => setActiveSection('features-knowledge-base')}
                        className="text-[#ff6b35] hover:text-[#e55a2b] font-medium flex items-center gap-1 transition-colors"
                      >
                        Upload docs <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-indigo-50 to-indigo-100/50 p-6 rounded-xl border border-indigo-200 hover:shadow-lg hover:border-indigo-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <CogIcon className="h-5 w-5 text-indigo-600" />
                        Custom Training
                      </h3>
                      <p className="text-gray-600 mb-4">Train models with your specific data and requirements</p>
                      <button
                        onClick={() => setActiveSection('features-custom-training')}
                        className="text-indigo-600 hover:text-indigo-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Start training <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-teal-50 to-teal-100/50 p-6 rounded-xl border border-teal-200 hover:shadow-lg hover:border-teal-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <BoltIcon className="h-5 w-5 text-teal-600" />
                        Async Processing
                      </h3>
                      <p className="text-gray-600 mb-4">Handle long-running tasks with webhook notifications</p>
                      <button
                        onClick={() => setActiveSection('features-async-processing')}
                        className="text-teal-600 hover:text-teal-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        Process async <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100/50 p-6 rounded-xl border border-yellow-200 hover:shadow-lg hover:border-yellow-300 transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
                        <ChartBarIcon className="h-5 w-5 text-yellow-600" />
                        Analytics & Monitoring
                      </h3>
                      <p className="text-gray-600 mb-4">Real-time insights and performance monitoring</p>
                      <button
                        onClick={() => setActiveSection('features-analytics-monitoring')}
                        className="text-yellow-600 hover:text-yellow-700 font-medium flex items-center gap-1 transition-colors"
                      >
                        View analytics <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Multi-Role Orchestration</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey's advanced LangGraph integration enables sophisticated multi-role task orchestration with automatic workflow detection and management.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Workflow Types</h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• <strong>Sequential:</strong> Single roles, step-by-step processing</li>
                            <li>• <strong>Supervisor:</strong> 2-3 roles with coordinated execution</li>
                            <li>• <strong>Hierarchical:</strong> 4+ roles or complex browsing tasks</li>
                            <li>• <strong>Auto:</strong> Intelligent workflow selection</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Advanced Features</h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• <strong>Memory Management:</strong> Context preservation across roles</li>
                            <li>• <strong>Streaming Support:</strong> Real-time progress updates</li>
                            <li>• <strong>Error Recovery:</strong> Automatic retry and fallback</li>
                            <li>• <strong>Progress Tracking:</strong> Detailed execution monitoring</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Provider Support</h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">OpenAI</div>
                        <div className="text-xs text-gray-600">GPT-4, GPT-3.5, o1</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Anthropic</div>
                        <div className="text-xs text-gray-600">Claude 3.5, Claude 3</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">Google</div>
                        <div className="text-xs text-gray-600">Gemini Pro, Flash</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">DeepSeek</div>
                        <div className="text-xs text-gray-600">V2.5, Coder</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">xAI</div>
                        <div className="text-xs text-gray-600">Grok Models</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <div className="font-semibold text-gray-900 mb-1">OpenRouter</div>
                        <div className="text-xs text-gray-600">300+ Models</div>
                      </div>
                    </div>
                    <p className="text-gray-600 mt-6 text-center">
                      Access to <strong>300+ AI models</strong> from leading providers with unified API interface
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Advanced Capabilities</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">RouKey Semantic Caching</h3>
                        <p className="text-gray-600 mb-4">
                          RouKey's intelligent caching system uses advanced semantic analysis and reranking for faster responses and reduced costs.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• RouKey embedding-based similarity detection</li>
                          <li>• Multiple API key rotation (9+ keys supported)</li>
                          <li>• Advanced reranking for result optimization</li>
                          <li>• Automatic cache invalidation</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Knowledge Base Integration</h3>
                        <p className="text-gray-600 mb-4">
                          Upload and integrate custom documents for enhanced AI responses with your specific knowledge.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Document upload and processing</li>
                          <li>• Automatic embedding generation</li>
                          <li>• Context-aware retrieval</li>
                          <li>• Tier-based document limits</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Custom Training</h3>
                        <p className="text-gray-600 mb-4">
                          Train models with your specific data and requirements for specialized use cases.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• File upload and processing</li>
                          <li>• Training job management</li>
                          <li>• Enhanced system prompts</li>
                          <li>• Multi-provider compatibility</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Async Processing</h3>
                        <p className="text-gray-600 mb-4">
                          Handle long-running tasks with asynchronous processing and webhook notifications.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Job submission and tracking</li>
                          <li>• Webhook notifications</li>
                          <li>• Progress monitoring</li>
                          <li>• Result retrieval</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Features Subsections */}
              {activeSection === 'features-intelligent-routing' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Intelligent Routing</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey's AI-powered routing engine automatically selects the optimal model for each request based on multiple factors.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Routing Strategies</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <BoltIcon className="h-5 w-5 text-blue-600" />
                          Intelligent Role Routing
                        </h3>
                        <p className="text-gray-600 mb-4">AI-powered classification routes requests based on detected roles (coding, writing, analysis, etc.) to specialized models.</p>
                        <div className="text-sm text-blue-600 font-medium">Best for: Multi-purpose applications</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <CircleStackIcon className="h-5 w-5 text-green-600" />
                          Complexity-Based Routing
                        </h3>
                        <p className="text-gray-600 mb-4">Analyzes prompt complexity (1-5 scale) and routes to appropriate models for optimal cost-performance balance.</p>
                        <div className="text-sm text-green-600 font-medium">Best for: Cost optimization</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <ListBulletIcon className="h-5 w-5 text-[#ff6b35]" />
                          Strict Fallback Strategy
                        </h3>
                        <p className="text-gray-600 mb-4">Ordered failover sequence with predictable routing and guaranteed fallback chain for maximum reliability.</p>
                        <div className="text-sm text-[#ff6b35] font-medium">Best for: Mission-critical applications</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 text-lg mb-3 flex items-center gap-2">
                          <SparklesIcon className="h-5 w-5 text-purple-600" />
                          Cost-Optimized Routing
                        </h3>
                        <p className="text-gray-600 mb-4">Smart cost optimization with learning algorithms that adapt to your usage patterns over time.</p>
                        <div className="text-sm text-purple-600 font-medium">Best for: Budget-conscious deployments</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">How Routing Works</h2>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                          <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span className="text-white font-bold text-xl">1</span>
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-3">Request Analysis</h3>
                          <p className="text-gray-600 text-sm">Gemini-powered classification analyzes request content, complexity, and context</p>
                        </div>
                        <div className="text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                          <div className="w-16 h-16 bg-[#ff6b35] rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span className="text-white font-bold text-xl">2</span>
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-3">Model Selection</h3>
                          <p className="text-gray-600 text-sm">Algorithm selects optimal model based on strategy, availability, and cost</p>
                        </div>
                        <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                          <div className="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span className="text-white font-bold text-xl">3</span>
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-3">Request Routing</h3>
                          <p className="text-gray-600 text-sm">Request is routed to selected model with automatic failover if needed</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Configuration Example</h2>
                    <p className="text-gray-600 mb-6">
                      Here's how to configure intelligent routing for a coding application:
                    </p>
                    <CodeBlock title="Intelligent routing configuration" language="json">
{`{
  "strategy": "intelligent_role",
  "roles": {
    "coding": {
      "primary": "deepseek-coder",
      "fallback": ["gpt-4", "claude-3-5-sonnet"],
      "complexity_threshold": 3
    },
    "writing": {
      "primary": "claude-3-5-sonnet",
      "fallback": ["gpt-4", "gpt-3.5-turbo"],
      "complexity_threshold": 2
    },
    "analysis": {
      "primary": "gpt-4",
      "fallback": ["claude-3-5-sonnet", "gemini-pro"],
      "complexity_threshold": 4
    }
  },
  "default_fallback": ["gpt-3.5-turbo"]
}`}
                    </CodeBlock>
                  </div>
                </div>
              )}

              {activeSection === 'features-semantic-caching' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Semantic Caching</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey's intelligent caching system uses advanced semantic analysis to reduce costs and improve response times.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Unlike traditional caching that requires exact matches, RouKey's semantic caching understands the meaning of requests and can serve cached responses for semantically similar queries.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CircleStackIcon className="h-5 w-5 text-blue-600" />
                            Jina Embeddings
                          </h3>
                          <p className="text-gray-600 mb-4">
                            Requests are converted to high-dimensional embeddings that capture semantic meaning.
                          </p>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• 9+ API keys in rotation</li>
                            <li>• Sub-second embedding generation</li>
                            <li>• Multi-language support</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <SparklesIcon className="h-5 w-5 text-green-600" />
                            Reranking
                          </h3>
                          <p className="text-gray-600 mb-4">
                            Jina reranker optimizes cache hit accuracy by reordering similarity results.
                          </p>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Improved relevance scoring</li>
                            <li>• Context-aware matching</li>
                            <li>• Reduced false positives</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Cache Performance</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <div className="text-3xl font-bold text-green-600 mb-2">85%</div>
                        <div className="text-gray-900 font-medium mb-1">Cache Hit Rate</div>
                        <div className="text-gray-600 text-sm">Average across all request types</div>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <div className="text-3xl font-bold text-blue-600 mb-2">50ms</div>
                        <div className="text-gray-900 font-medium mb-1">Response Time</div>
                        <div className="text-gray-600 text-sm">Average for cached responses</div>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                        <div className="text-3xl font-bold text-[#ff6b35] mb-2">90%</div>
                        <div className="text-gray-900 font-medium mb-1">Cost Reduction</div>
                        <div className="text-gray-600 text-sm">For cached requests</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Example Scenarios</h2>
                    <div className="space-y-6">
                      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                        <h4 className="font-medium text-gray-900 mb-3">Scenario: Similar Coding Questions</h4>
                        <div className="space-y-2">
                          <div className="p-3 bg-white rounded border-l-4 border-blue-500">
                            <div className="text-sm text-gray-600 mb-1">Original Request:</div>
                            <div className="text-gray-900">"Write a Python function to sort a list"</div>
                          </div>
                          <div className="p-3 bg-green-50 rounded border-l-4 border-green-500">
                            <div className="text-sm text-gray-600 mb-1">Cache Hit:</div>
                            <div className="text-gray-900">"Create a Python function for sorting an array"</div>
                          </div>
                        </div>
                        <div className="mt-4 text-sm text-gray-600">
                          <strong>Result:</strong> 95% similarity match, cached response served in 45ms
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Multi-Role Orchestration Section */}
              {activeSection === 'features-multi-role-orchestration' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Multi-Role Orchestration</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey's advanced LangGraph integration enables sophisticated multi-role task orchestration with automatic workflow detection and intelligent coordination.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Workflow Types</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ArrowRightIcon className="h-5 w-5 text-blue-600" />
                          Sequential Workflow
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Single roles processed step-by-step for straightforward tasks.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Linear task progression</li>
                          <li>• Optimal for single-domain tasks</li>
                          <li>• Fast execution</li>
                          <li>• Minimal overhead</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <UserGroupIcon className="h-5 w-5 text-green-600" />
                          Supervisor Workflow
                        </h3>
                        <p className="text-gray-600 mb-4">
                          2-3 roles with coordinated execution and intelligent supervision.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Coordinated multi-role tasks</li>
                          <li>• Intelligent task delegation</li>
                          <li>• Quality control</li>
                          <li>• Parallel processing</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <BuildingOfficeIcon className="h-5 w-5 text-purple-600" />
                          Hierarchical Workflow
                        </h3>
                        <p className="text-gray-600 mb-4">
                          4+ roles for complex tasks requiring specialized expertise and browsing.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Complex task decomposition</li>
                          <li>• Specialized role assignment</li>
                          <li>• Web browsing capabilities</li>
                          <li>• Memory tracking</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Automatic Detection</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey uses advanced AI classification to automatically detect when multi-role orchestration is needed and selects the appropriate workflow type.
                      </p>
                      <div className="bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Detection Process</h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">1</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Request Analysis</div>
                            <div className="text-sm text-gray-600">Analyze complexity and requirements</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">2</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Role Detection</div>
                            <div className="text-sm text-gray-600">Identify required specialized roles</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">3</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Workflow Selection</div>
                            <div className="text-sm text-gray-600">Choose optimal orchestration pattern</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-[#ff6b35] rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">4</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Execution</div>
                            <div className="text-sm text-gray-600">Coordinate and execute tasks</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Streaming & Performance</h2>
                    <div className="space-y-6">
                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Automatic Streaming</h3>
                        <p className="text-gray-600 mb-4">
                          Multi-role tasks automatically enable streaming to provide real-time progress updates and avoid timeout issues.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Benefits</h4>
                            <ul className="text-gray-600 text-sm space-y-1">
                              <li>• Real-time progress visibility</li>
                              <li>• No 60-second timeout limits</li>
                              <li>• Better user experience</li>
                              <li>• Immediate feedback</li>
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Implementation</h4>
                            <ul className="text-gray-600 text-sm space-y-1">
                              <li>• Automatic stream=true detection</li>
                              <li>• Chunked response delivery</li>
                              <li>• Progress indicators</li>
                              <li>• Error handling</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Alert type="info">
                    <strong>Recommended:</strong> For complex multi-role tasks, streaming is automatically enabled to provide the best user experience and avoid timeout issues on platforms like Vercel.
                  </Alert>
                </div>
              )}

              {/* Knowledge Base Section */}
              {activeSection === 'features-knowledge-base' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Knowledge Base</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Enhance your AI responses with custom knowledge by uploading documents that provide domain-specific context and expertise.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Document Upload & Processing</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Upload documents in various formats to create a custom knowledge base that enhances AI responses with your proprietary information.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <DocumentIcon className="h-5 w-5 text-blue-600" />
                            Supported Formats
                          </h3>
                          <ul className="text-gray-600 space-y-2">
                            <li>• PDF documents</li>
                            <li>• Word documents (.docx)</li>
                            <li>• Text files (.txt)</li>
                            <li>• Markdown files (.md)</li>
                            <li>• CSV files</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CogIcon className="h-5 w-5 text-green-600" />
                            Processing Features
                          </h3>
                          <ul className="text-gray-600 space-y-2">
                            <li>• Automatic text extraction</li>
                            <li>• Intelligent chunking</li>
                            <li>• Semantic indexing</li>
                            <li>• Context preservation</li>
                            <li>• Metadata extraction</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Tier Limits & Usage</h2>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Tier</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Max Documents</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">File Size Limit</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Features</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Free</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">-</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Upgrade required</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Starter</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">-</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Upgrade required</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Professional</td>
                            <td className="px-6 py-4 text-sm text-gray-600">5 documents</td>
                            <td className="px-6 py-4 text-sm text-gray-600">10MB per file</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Full knowledge base access</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Enterprise</td>
                            <td className="px-6 py-4 text-sm text-gray-600">15 documents</td>
                            <td className="px-6 py-4 text-sm text-gray-600">25MB per file</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Advanced features, priority processing</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Intelligent Context Retrieval</h3>
                        <p className="text-gray-600 mb-4">
                          When you ask a question, RouKey automatically searches your knowledge base for relevant information and includes it in the AI's context.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">1</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Query Analysis</div>
                            <div className="text-sm text-gray-600">Understand user intent</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">2</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Document Search</div>
                            <div className="text-sm text-gray-600">Find relevant content</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">3</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Context Injection</div>
                            <div className="text-sm text-gray-600">Add to AI prompt</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">4</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Enhanced Response</div>
                            <div className="text-sm text-gray-600">AI responds with context</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Best Practice:</strong> Upload well-structured documents with clear headings and sections for optimal knowledge extraction and retrieval accuracy.
                  </Alert>
                </div>
              )}

              {/* Custom Training Section */}
              {activeSection === 'features-custom-training' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Custom Training</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Train AI models with your specific data, instructions, and behavioral patterns to create domain-specific assistants tailored to your needs.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Training Methods</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <DocumentTextIcon className="h-5 w-5 text-blue-600" />
                          Prompt Engineering
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Define AI behavior through custom system prompts, instructions, and example interactions.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• System instructions</li>
                          <li>• Behavioral guidelines</li>
                          <li>• Example conversations</li>
                          <li>• Response formatting</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <AcademicCapIcon className="h-5 w-5 text-green-600" />
                          Fine-tuning (Coming Soon)
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Advanced model customization with your specific datasets for specialized performance.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Custom model weights</li>
                          <li>• Domain specialization</li>
                          <li>• Performance optimization</li>
                          <li>• Proprietary knowledge</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Training Format Guide</h2>
                    <div className="space-y-6">
                      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Supported Formats</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">System Instructions</h4>
                            <div className="bg-white p-3 rounded border font-mono text-sm">
                              SYSTEM: You are a helpful customer service agent for our company<br/>
                              BEHAVIOR: Always be polite and offer solutions
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Example Interactions</h4>
                            <div className="bg-white p-3 rounded border font-mono text-sm">
                              User asks about returns → I'd be happy to help with your return!<br/>
                              Customer is frustrated → I understand your frustration. Let me help.
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">General Instructions</h4>
                            <div className="bg-white p-3 rounded border font-mono text-sm">
                              Always provide step-by-step explanations for technical topics.<br/>
                              Include relevant examples when explaining concepts.
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Tier Availability</h2>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Tier</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Prompt Engineering</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Fine-tuning</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Custom Models</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Free</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Starter</td>
                            <td className="px-6 py-4 text-sm text-green-600">✓ Available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Professional</td>
                            <td className="px-6 py-4 text-sm text-green-600">✓ Available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Enterprise</td>
                            <td className="px-6 py-4 text-sm text-green-600">✓ Available</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Coming soon</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <Alert type="info">
                    <strong>Getting Started:</strong> Custom training through prompt engineering is available for Starter tier and above. Visit the Training page in your dashboard to create custom prompts and behavioral instructions.
                  </Alert>
                </div>
              )}

              {/* Async Processing Section */}
              {activeSection === 'features-async-processing' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Async Processing</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Handle long-running AI tasks with webhook notifications, perfect for complex multi-role workflows and time-intensive operations.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">How It Works</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Submit tasks for background processing and receive results via webhooks when complete, eliminating timeout constraints.
                      </p>
                      <div className="bg-gradient-to-r from-teal-50 to-teal-100 p-6 rounded-xl border border-teal-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Processing Flow</h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">1</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Submit Job</div>
                            <div className="text-sm text-gray-600">Send request with webhook URL</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">2</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Queue Processing</div>
                            <div className="text-sm text-gray-600">Task added to processing queue</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">3</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Background Execution</div>
                            <div className="text-sm text-gray-600">AI processes without timeout</div>
                          </div>
                          <div className="text-center">
                            <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                              <span className="text-white font-bold">4</span>
                            </div>
                            <div className="font-medium text-gray-900 mb-1">Webhook Notification</div>
                            <div className="text-sm text-gray-600">Results sent to your endpoint</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">API Usage</h2>
                    <div className="space-y-6">
                      <h3 className="text-xl font-semibold text-gray-900">Submit Async Job</h3>
                      <CodeBlock title="Submit async processing job" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/async/submit" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Analyze this complex dataset and provide insights"}
    ],
    "webhook_url": "https://your-app.com/webhook/roukey",
    "metadata": {
      "task_id": "analysis_001",
      "priority": "high"
    }
  }'`}
                      </CodeBlock>

                      <h3 className="text-xl font-semibold text-gray-900">Check Job Status</h3>
                      <CodeBlock title="Check async job status" language="bash">
{`curl -X GET "https://roukey.online/api/external/v1/async/status/job_id_here" \\
  -H "X-API-Key: rk_live_your_api_key_here"`}
                      </CodeBlock>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Webhook Integration</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Receive job completion notifications at your specified webhook endpoint with full results and metadata.
                      </p>
                      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Webhook Payload Example</h3>
                        <CodeBlock title="Webhook notification payload" language="json">
{`{
  "job_id": "async_job_123456",
  "status": "completed",
  "result": {
    "response": "Analysis complete: The dataset shows...",
    "roles_used": ["analyst", "researcher"],
    "processing_time": 45.2,
    "tokens_used": 2847
  },
  "metadata": {
    "task_id": "analysis_001",
    "priority": "high"
  },
  "completed_at": "2024-06-24T10:30:00Z"
}`}
                        </CodeBlock>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Best Use Cases:</strong> Async processing is ideal for complex multi-role tasks, large document analysis, research workflows, and any operation that might exceed standard timeout limits.
                  </Alert>
                </div>
              )}

              {/* Analytics & Monitoring Section */}
              {activeSection === 'features-analytics-monitoring' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Analytics & Monitoring</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Comprehensive real-time insights into your AI usage, performance metrics, and cost optimization opportunities.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Performance Metrics</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ClockIcon className="h-5 w-5 text-blue-600" />
                          Response Times
                        </h3>
                        <ul className="text-gray-600 space-y-2">
                          <li>• First token latency tracking</li>
                          <li>• Complete response timing</li>
                          <li>• Provider comparison</li>
                          <li>• Performance trends</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
                          Cost Analytics
                        </h3>
                        <ul className="text-gray-600 space-y-2">
                          <li>• Token usage tracking</li>
                          <li>• Cost per request</li>
                          <li>• Provider cost comparison</li>
                          <li>• Budget monitoring</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ChartBarIcon className="h-5 w-5 text-purple-600" />
                          Usage Statistics
                        </h3>
                        <ul className="text-gray-600 space-y-2">
                          <li>• Request volume trends</li>
                          <li>• Model usage distribution</li>
                          <li>• Cache hit rates</li>
                          <li>• Error rate monitoring</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Real-time Monitoring</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Monitor your AI infrastructure in real-time with comprehensive dashboards and alerting capabilities.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-yellow-50 rounded-xl border border-yellow-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                            Health Monitoring
                          </h3>
                          <ul className="text-gray-600 space-y-2">
                            <li>• API endpoint health checks</li>
                            <li>• Provider availability status</li>
                            <li>• Automatic failover tracking</li>
                            <li>• System uptime monitoring</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-red-50 rounded-xl border border-red-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <BellIcon className="h-5 w-5 text-red-600" />
                            Alert System
                          </h3>
                          <ul className="text-gray-600 space-y-2">
                            <li>• High error rate alerts</li>
                            <li>• Cost threshold notifications</li>
                            <li>• Performance degradation warnings</li>
                            <li>• Custom alert rules</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Performance Targets</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">First Token Performance</h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                            <div className="text-2xl font-bold text-green-600 mb-1">⚡</div>
                            <div className="font-medium text-gray-900 mb-1">Excellent</div>
                            <div className="text-sm text-gray-600">&lt; 500ms</div>
                          </div>
                          <div className="text-center p-4 bg-white rounded-lg border border-blue-200">
                            <div className="text-2xl font-bold text-blue-600 mb-1">✅</div>
                            <div className="font-medium text-gray-900 mb-1">Good</div>
                            <div className="text-sm text-gray-600">500-1000ms</div>
                          </div>
                          <div className="text-center p-4 bg-white rounded-lg border border-yellow-200">
                            <div className="text-2xl font-bold text-yellow-600 mb-1">⚠️</div>
                            <div className="font-medium text-gray-900 mb-1">Slow</div>
                            <div className="text-sm text-gray-600">1000-2000ms</div>
                          </div>
                          <div className="text-center p-4 bg-white rounded-lg border border-red-200">
                            <div className="text-2xl font-bold text-red-600 mb-1">🐌</div>
                            <div className="font-medium text-gray-900 mb-1">Very Slow</div>
                            <div className="text-sm text-gray-600">&gt; 2000ms</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Tier Features</h2>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Tier</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Analytics</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Monitoring</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Alerts</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Free</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Basic usage stats</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Basic monitoring</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Not available</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Starter</td>
                            <td className="px-6 py-4 text-sm text-green-600">Enhanced analytics</td>
                            <td className="px-6 py-4 text-sm text-green-600">Real-time monitoring</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Basic alerts</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Professional</td>
                            <td className="px-6 py-4 text-sm text-green-600">Advanced analytics</td>
                            <td className="px-6 py-4 text-sm text-green-600">Comprehensive monitoring</td>
                            <td className="px-6 py-4 text-sm text-green-600">Custom alerts</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Enterprise</td>
                            <td className="px-6 py-4 text-sm text-green-600">Full analytics suite</td>
                            <td className="px-6 py-4 text-sm text-green-600">Enterprise monitoring</td>
                            <td className="px-6 py-4 text-sm text-green-600">Advanced alerting</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <Alert type="info">
                    <strong>Performance Optimization:</strong> Use the analytics dashboard to identify bottlenecks, optimize routing strategies, and reduce costs while maintaining response quality.
                  </Alert>
                </div>
              )}

              {activeSection === 'authentication' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Authentication</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Learn how to authenticate with RouKey using API keys.
                    </p>
                  </div>

                  <Alert type="info">
                    <strong>Important:</strong> RouKey uses the <code className="bg-blue-100 px-2 py-1 rounded text-blue-800">X-API-Key</code> header for authentication.
                    Never use <code className="bg-blue-100 px-2 py-1 rounded text-blue-800">Authorization</code> header format.
                  </Alert>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Getting Your API Key</h2>
                    <div className="space-y-4">
                      <p className="text-gray-600 text-lg">
                        To get started with RouKey, you'll need to create an API key from your dashboard:
                      </p>
                      <ol className="list-decimal list-inside space-y-3 text-gray-600 ml-4 text-lg">
                        <li>Sign up for a RouKey account at <a href="https://roukey.online" className="text-[#ff6b35] hover:text-[#e55a2b] underline">roukey.online</a></li>
                        <li>Navigate to your dashboard and create a configuration</li>
                        <li>Add your LLM provider API keys (OpenAI, Anthropic, etc.)</li>
                        <li>Generate a user API key for external access</li>
                        <li>Copy your API key (format: <code className="bg-gray-100 px-2 py-1 rounded text-gray-700">rk_live_...</code>)</li>
                      </ol>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Authentication Methods</h2>
                    <div className="space-y-8">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Method 1: X-API-Key Header (Recommended)</h3>
                        <CodeBlock title="Using X-API-Key header" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Method 2: Bearer Token</h3>
                        <CodeBlock title="Using Authorization Bearer header" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'`}
                        </CodeBlock>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Best Practice:</strong> Always use the <code className="bg-green-100 px-2 py-1 rounded text-green-800">X-API-Key</code> header method
                    as it's the primary authentication method for RouKey and ensures maximum compatibility.
                  </Alert>
                </div>
              )}

              {/* Authentication Subsections */}
              {activeSection === 'authentication-api-keys' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">API Keys</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Learn how to create, manage, and secure your RouKey API keys for optimal performance and security.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Creating API Keys</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey API keys are generated from your dashboard and provide secure access to the intelligent routing system.
                      </p>
                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">Key Format</h3>
                        <div className="font-mono text-sm bg-white p-3 rounded border">
                          rk_live_1234567890abcdef...
                        </div>
                        <p className="text-gray-600 text-sm mt-2">
                          All RouKey API keys start with <code className="bg-white px-1 rounded">rk_live_</code> followed by a secure random string.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Key Management</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <KeyIcon className="h-5 w-5 text-green-600" />
                          Best Practices
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Store keys in environment variables</li>
                          <li>• Never commit keys to version control</li>
                          <li>• Rotate keys regularly</li>
                          <li>• Use different keys for different environments</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-red-50 rounded-xl border border-red-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
                          Security Warnings
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Never expose keys in client-side code</li>
                          <li>• Don't share keys in public channels</li>
                          <li>• Revoke compromised keys immediately</li>
                          <li>• Monitor key usage for anomalies</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Tier Limits</h2>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Tier</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Max API Keys</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Features</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Free</td>
                            <td className="px-6 py-4 text-sm text-gray-600">3</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Basic routing, 1 config</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Starter</td>
                            <td className="px-6 py-4 text-sm text-gray-600">50</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Advanced routing, custom roles</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Professional</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Unlimited</td>
                            <td className="px-6 py-4 text-sm text-gray-600">All features, knowledge base</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-medium text-gray-900">Enterprise</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Unlimited</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Enterprise features, priority support</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'authentication-security' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Security</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey implements enterprise-grade security measures to protect your API keys and data.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Encryption & Storage</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                          AES-256 Encryption
                        </h3>
                        <p className="text-gray-600 mb-4">
                          All API keys are encrypted using AES-256 encryption before storage in our secure database.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• Keys encrypted at rest</li>
                          <li>• Secure key derivation</li>
                          <li>• Regular key rotation</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CloudIcon className="h-5 w-5 text-green-600" />
                          Transit Security
                        </h3>
                        <p className="text-gray-600 mb-4">
                          All communications use TLS 1.3 encryption to protect data in transit.
                        </p>
                        <ul className="text-gray-600 text-sm space-y-1">
                          <li>• TLS 1.3 encryption</li>
                          <li>• Certificate pinning</li>
                          <li>• HSTS headers</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Data Privacy</h2>
                    <div className="space-y-6">
                      <div className="bg-green-50 p-6 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3">What We DON'T Store</h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Your actual API responses from providers</li>
                          <li>• Sensitive content from your requests</li>
                          <li>• Personal data beyond what's necessary</li>
                          <li>• Provider API keys in plain text</li>
                        </ul>
                      </div>
                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">What We Store</h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Encrypted provider API keys</li>
                          <li>• Request metadata for routing decisions</li>
                          <li>• Usage analytics (anonymized)</li>
                          <li>• Configuration settings</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Compliance</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                        <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <ShieldCheckIcon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">SOC 2 Ready</h3>
                        <p className="text-gray-600 text-sm">Security controls aligned with SOC 2 Type II requirements</p>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <DocumentTextIcon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">GDPR Compliant</h3>
                        <p className="text-gray-600 text-sm">Full compliance with European data protection regulations</p>
                      </div>
                      <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <KeyIcon className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">BYOK Model</h3>
                        <p className="text-gray-600 text-sm">You maintain control of your API keys and data</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Authentication Methods Section */}
              {activeSection === 'authentication-auth-methods' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Authentication Methods</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey supports multiple authentication methods to integrate seamlessly with your existing infrastructure and security requirements.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Supported Methods</h2>
                    <div className="space-y-8">
                      <div className="border-l-4 border-[#ff6b35] pl-6">
                        <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <KeyIcon className="h-5 w-5 text-[#ff6b35]" />
                          X-API-Key Header (Recommended)
                        </h3>
                        <p className="text-gray-600 mb-4">
                          The primary and recommended authentication method for RouKey. This method provides the best compatibility and performance.
                        </p>
                        <CodeBlock title="X-API-Key authentication" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}]
  }'`}
                        </CodeBlock>
                        <div className="mt-4 bg-green-50 p-4 rounded-lg border border-green-200">
                          <h4 className="font-medium text-gray-900 mb-2">Benefits:</h4>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Primary authentication method</li>
                            <li>• Maximum compatibility</li>
                            <li>• Optimal performance</li>
                            <li>• Clear separation from OAuth tokens</li>
                          </ul>
                        </div>
                      </div>

                      <div className="border-l-4 border-blue-500 pl-6">
                        <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                          Authorization Bearer Token
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Alternative authentication method using the standard Authorization header with Bearer token format.
                        </p>
                        <CodeBlock title="Bearer token authentication" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}]
  }'`}
                        </CodeBlock>
                        <div className="mt-4 bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <h4 className="font-medium text-gray-900 mb-2">Use Cases:</h4>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Legacy system integration</li>
                            <li>• Standard OAuth workflows</li>
                            <li>• Third-party tool compatibility</li>
                            <li>• Enterprise security requirements</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">SDK Integration</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey is compatible with popular AI SDKs. Here's how to configure authentication for different platforms:
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">OpenAI SDK (Python)</h3>
                          <CodeBlock title="OpenAI Python SDK configuration" language="python">
{`from openai import OpenAI

client = OpenAI(
    api_key="rk_live_your_api_key_here",
    base_url="https://roukey.online/api/external/v1"
)

response = client.chat.completions.create(
    messages=[{"role": "user", "content": "Hello!"}],
    model="gpt-4"  # This will be routed by RouKey
)`}
                          </CodeBlock>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">OpenAI SDK (JavaScript)</h3>
                          <CodeBlock title="OpenAI JavaScript SDK configuration" language="javascript">
{`import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: 'rk_live_your_api_key_here',
  baseURL: 'https://roukey.online/api/external/v1'
});

const response = await openai.chat.completions.create({
  messages: [{ role: 'user', content: 'Hello!' }],
  model: 'gpt-4' // This will be routed by RouKey
});`}
                          </CodeBlock>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Security Best Practices</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CheckIcon className="h-5 w-5 text-green-600" />
                          Recommended Practices
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Use environment variables for API keys</li>
                          <li>• Implement key rotation policies</li>
                          <li>• Monitor API key usage patterns</li>
                          <li>• Use different keys for different environments</li>
                          <li>• Implement proper error handling</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-red-50 rounded-xl border border-red-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
                          Security Warnings
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Never expose keys in client-side code</li>
                          <li>• Don't commit keys to version control</li>
                          <li>• Avoid logging API keys</li>
                          <li>• Don't share keys in public channels</li>
                          <li>• Revoke compromised keys immediately</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Recommendation:</strong> Use the X-API-Key header method for new integrations as it provides the best performance and compatibility with RouKey's routing system.
                  </Alert>
                </div>
              )}

              {/* Rate Limiting Section */}
              {activeSection === 'authentication-rate-limiting' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Rate Limiting</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey provides unlimited API requests across all tiers, focusing on feature-based restrictions rather than rate limiting.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">No Rate Limits Policy</h2>
                    <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
                      <div className="flex items-center gap-3 mb-4">
                        <CheckIcon className="h-6 w-6 text-green-600" />
                        <h3 className="text-xl font-semibold text-gray-900">Unlimited API Requests</h3>
                      </div>
                      <p className="text-gray-600 mb-4">
                        RouKey does not impose rate limits on any subscription tier. You can make as many API requests as needed without worrying about request quotas or throttling.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                          <div className="text-2xl font-bold text-green-600 mb-1">∞</div>
                          <div className="font-medium text-gray-900 mb-1">Free Tier</div>
                          <div className="text-sm text-gray-600">Unlimited requests</div>
                        </div>
                        <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                          <div className="text-2xl font-bold text-green-600 mb-1">∞</div>
                          <div className="font-medium text-gray-900 mb-1">Paid Tiers</div>
                          <div className="text-sm text-gray-600">Unlimited requests</div>
                        </div>
                        <div className="text-center p-4 bg-white rounded-lg border border-green-200">
                          <div className="text-2xl font-bold text-green-600 mb-1">∞</div>
                          <div className="font-medium text-gray-900 mb-1">Enterprise</div>
                          <div className="text-sm text-gray-600">Unlimited requests</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">What We Limit Instead</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        Instead of rate limiting, RouKey uses feature-based restrictions to differentiate subscription tiers:
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CogIcon className="h-5 w-5 text-blue-600" />
                            Configuration Limits
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Free: 1 configuration</li>
                            <li>• Starter: 4 configurations</li>
                            <li>• Professional: Unlimited</li>
                            <li>• Enterprise: Unlimited</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-purple-50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <KeyIcon className="h-5 w-5 text-purple-600" />
                            API Key Limits
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Free: 3 user-generated keys</li>
                            <li>• Starter: 50 user-generated keys</li>
                            <li>• Professional: Unlimited</li>
                            <li>• Enterprise: Unlimited</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <SparklesIcon className="h-5 w-5 text-green-600" />
                            Feature Access
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Advanced routing strategies</li>
                            <li>• Custom roles and training</li>
                            <li>• Knowledge base access</li>
                            <li>• Semantic caching</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-yellow-50 rounded-xl border border-yellow-200">
                          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <UserGroupIcon className="h-5 w-5 text-yellow-600" />
                            Usage Limits
                          </h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Browsing tasks per month</li>
                            <li>• Knowledge base documents</li>
                            <li>• Custom roles per config</li>
                            <li>• Multi-role orchestration</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Provider Rate Limits</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        While RouKey doesn't impose rate limits, your underlying AI providers (OpenAI, Anthropic, etc.) may have their own rate limits:
                      </p>
                      <div className="bg-yellow-50 p-6 rounded-xl border border-yellow-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                          Provider Limitations
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• <strong>OpenAI:</strong> Rate limits based on your OpenAI tier</li>
                          <li>• <strong>Anthropic:</strong> Rate limits based on your Claude usage tier</li>
                          <li>• <strong>Google:</strong> Free tier limited to ~60 requests/minute</li>
                          <li>• <strong>Other providers:</strong> Each has their own rate limiting policies</li>
                        </ul>
                      </div>
                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">How RouKey Handles Provider Limits</h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Automatic failover to alternative providers when limits are hit</li>
                          <li>• Intelligent routing to avoid known rate-limited providers</li>
                          <li>• Detection of provider free tiers with lower limits</li>
                          <li>• Load balancing across multiple API keys for the same provider</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Best Practices</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-green-50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <CheckIcon className="h-5 w-5 text-green-600" />
                          Optimization Tips
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Use semantic caching to reduce duplicate requests</li>
                          <li>• Configure multiple API keys per provider for load balancing</li>
                          <li>• Enable streaming for long-running tasks</li>
                          <li>• Use intelligent routing to optimize costs</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-blue-50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                          Monitoring
                        </h3>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Monitor your provider API usage and costs</li>
                          <li>• Set up alerts for unusual usage patterns</li>
                          <li>• Use RouKey's analytics to track performance</li>
                          <li>• Implement proper error handling for provider limits</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <Alert type="info">
                    <strong>No Limits:</strong> RouKey focuses on providing unlimited access to AI models while optimizing costs and performance through intelligent routing rather than restricting usage.
                  </Alert>
                </div>
              )}

              {activeSection === 'api-reference' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">API Reference</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Complete reference for the RouKey API endpoints and parameters.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Base URL</h2>
                    <CodeBlock title="Production Base URL">
{`https://roukey.online/api/external/v1`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Chat Completions</h2>
                    <p className="text-gray-600 mb-6 text-lg">
                      Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.
                    </p>

                    <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl mb-6 border border-green-200">
                      <div className="flex items-center gap-3 mb-3">
                        <span className="bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium">POST</span>
                        <code className="text-gray-900 text-lg font-mono">/chat/completions</code>
                      </div>
                      <p className="text-gray-600">
                        OpenAI-compatible endpoint with RouKey's intelligent routing capabilities
                      </p>
                    </div>

                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Request Parameters</h3>
                    <div className="overflow-x-auto mb-8">
                      <table className="min-w-full bg-white border border-gray-200 rounded-xl shadow-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Parameter</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Type</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Required</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Description</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">messages</td>
                            <td className="px-6 py-4 text-sm text-gray-600">array</td>
                            <td className="px-6 py-4 text-sm text-green-600">Yes</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Array of message objects</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">stream</td>
                            <td className="px-6 py-4 text-sm text-gray-600">boolean</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Enable streaming responses (recommended for multi-role tasks)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">temperature</td>
                            <td className="px-6 py-4 text-sm text-gray-600">number</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Sampling temperature (0-2)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">max_tokens</td>
                            <td className="px-6 py-4 text-sm text-gray-600">integer</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">Maximum tokens to generate</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-[#ff6b35]">role</td>
                            <td className="px-6 py-4 text-sm text-gray-600">string</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-600">RouKey-specific role for routing (e.g., "coding", "writing")</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Example Request</h3>
                    <CodeBlock title="Basic chat completion" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Explain quantum computing"}
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 500
}`}
                    </CodeBlock>

                    <h3 className="text-xl font-semibold text-gray-900 mb-4 mt-8">Example with Role-Based Routing</h3>
                    <CodeBlock title="Role-based routing request" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Write a Python function to sort a list"}
  ],
  "role": "coding",
  "stream": true,
  "max_tokens": 1000
}`}
                    </CodeBlock>

                    <Alert type="tip">
                      <strong>Streaming Recommended:</strong> For complex tasks that may involve multiple roles or require significant processing,
                      use <code className="bg-green-100 px-2 py-1 rounded text-green-800">stream: true</code> to avoid timeouts and get real-time responses.
                    </Alert>
                  </div>
                </div>
              )}

              {activeSection === 'use-cases' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">
                      Use Cases
                    </h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Discover how RouKey can optimize your specific use case with intelligent routing and cost savings.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Development & Coding</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Code Generation & Review</h3>
                        <p className="text-gray-600 mb-4">
                          Route coding tasks to specialized models like DeepSeek Coder for optimal code quality and cost efficiency.
                        </p>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2">Recommended Strategy:</h4>
                          <p className="text-gray-600 text-sm">Intelligent Role Routing with "coding" role detection</p>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Documentation & Comments</h3>
                        <p className="text-gray-600 mb-4">
                          Automatically route documentation tasks to cost-effective models while maintaining quality.
                        </p>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2">Cost Savings:</h4>
                          <p className="text-gray-600 text-sm">Up to 70% reduction using complexity-based routing</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Content Creation</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Blog Posts & Articles</h3>
                        <p className="text-gray-600 mb-4">
                          Route creative writing tasks to models optimized for content generation with appropriate complexity analysis.
                        </p>
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <h4 className="font-medium text-gray-900 mb-2">Multi-Role Workflow:</h4>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Research and outline generation</li>
                            <li>• Content writing and expansion</li>
                            <li>• Editing and refinement</li>
                            <li>• SEO optimization</li>
                          </ul>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Marketing Copy</h3>
                        <p className="text-gray-600 mb-4">
                          Optimize marketing content creation with role-based routing for different content types.
                        </p>
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <h4 className="font-medium text-gray-900 mb-2">Specialized Roles:</h4>
                          <ul className="text-gray-600 text-sm space-y-1">
                            <li>• Email campaigns</li>
                            <li>• Social media posts</li>
                            <li>• Ad copy generation</li>
                            <li>• Product descriptions</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Enterprise Applications</h2>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Customer Support</h3>
                          <p className="text-gray-600 mb-4">Intelligent routing based on query complexity and urgency.</p>
                          <div className="text-sm text-purple-600 font-medium">Strategy: Complexity + Fallback</div>
                        </div>
                        <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Data Analysis</h3>
                          <p className="text-gray-600 mb-4">Route analytical tasks to models optimized for reasoning and computation.</p>
                          <div className="text-sm text-blue-600 font-medium">Strategy: Role-based Routing</div>
                        </div>
                        <div className="p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Document Processing</h3>
                          <p className="text-gray-600 mb-4">Efficient processing of large document sets with cost optimization.</p>
                          <div className="text-sm text-green-600 font-medium">Strategy: Cost-optimized</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Educational Platforms</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Personalized Learning</h3>
                        <p className="text-gray-600 mb-4">
                          Adapt content difficulty and teaching style based on student needs with intelligent routing.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Beginner explanations → Cost-effective models</li>
                          <li>• Advanced topics → Premium models</li>
                          <li>• Interactive exercises → Specialized models</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Content Generation</h3>
                        <p className="text-gray-600 mb-4">
                          Generate educational content at scale while maintaining quality and controlling costs.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Quiz generation</li>
                          <li>• Lesson plan creation</li>
                          <li>• Assignment feedback</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Research & Analysis</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg">
                        RouKey's multi-role orchestration excels at complex research workflows that require coordination between different AI capabilities.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Market Research</h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Data collection and web browsing</li>
                            <li>• Competitive analysis</li>
                            <li>• Trend identification</li>
                            <li>• Report generation</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Academic Research</h3>
                          <ul className="space-y-2 text-gray-600">
                            <li>• Literature review</li>
                            <li>• Data analysis</li>
                            <li>• Hypothesis generation</li>
                            <li>• Paper writing assistance</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white">
                    <h3 className="text-xl font-semibold mb-4">Ready to optimize your use case?</h3>
                    <p className="text-white/90 mb-6">
                      Contact our team to discuss how RouKey can be tailored to your specific requirements and use case.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <button
                        onClick={() => setActiveSection('getting-started')}
                        className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                      >
                        Get Started
                      </button>
                      <a
                        href="mailto:<EMAIL>"
                        className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors text-center"
                      >
                        Contact Sales
                      </a>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'examples' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Examples</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Practical examples to get you started with RouKey in different programming languages.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">JavaScript/Node.js</h2>
                    <CodeBlock title="Basic chat completion with fetch" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Explain machine learning in simple terms' }
    ],
    stream: false,
    max_tokens: 500
  })
});

const data = await response.json();
console.log(data.choices[0].message.content);`}
                    </CodeBlock>

                    <CodeBlock title="Streaming response example" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Write a detailed explanation of quantum computing' }
    ],
    stream: true,
    max_tokens: 1000
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  const chunk = decoder.decode(value);
  const lines = chunk.split('\\n');

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') return;

      try {
        const parsed = JSON.parse(data);
        const content = parsed.choices[0]?.delta?.content;
        if (content) {
          process.stdout.write(content);
        }
      } catch (e) {
        // Skip invalid JSON
      }
    }
  }
}`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Python</h2>
                    <CodeBlock title="Basic chat completion with requests" language="python">
{`import requests
import json

response = requests.post(
    'https://roukey.online/api/external/v1/chat/completions',
    headers={
        'Content-Type': 'application/json',
        'X-API-Key': 'rk_live_your_api_key_here'
    },
    json={
        'messages': [
            {'role': 'user', 'content': 'Explain machine learning in simple terms'}
        ],
        'stream': False,
        'max_tokens': 500
    }
)

data = response.json()
print(data['choices'][0]['message']['content'])`}
                    </CodeBlock>

                    <CodeBlock title="Streaming response with requests" language="python">
{`import requests
import json

response = requests.post(
    'https://roukey.online/api/external/v1/chat/completions',
    headers={
        'Content-Type': 'application/json',
        'X-API-Key': 'rk_live_your_api_key_here'
    },
    json={
        'messages': [
            {'role': 'user', 'content': 'Write a detailed explanation of quantum computing'}
        ],
        'stream': True,
        'max_tokens': 1000
    },
    stream=True
)

for line in response.iter_lines():
    if line:
        line = line.decode('utf-8')
        if line.startswith('data: '):
            data = line[6:]
            if data == '[DONE]':
                break
            try:
                parsed = json.loads(data)
                content = parsed['choices'][0]['delta'].get('content', '')
                if content:
                    print(content, end='', flush=True)
            except json.JSONDecodeError:
                continue`}
                    </CodeBlock>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">cURL</h2>
                    <CodeBlock title="Basic request with cURL" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "stream": false,
    "max_tokens": 150
  }'`}
                    </CodeBlock>

                    <CodeBlock title="Role-based routing with cURL" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}
    ],
    "role": "coding",
    "stream": true,
    "max_tokens": 500
  }'`}
                    </CodeBlock>
                  </div>

                  <Alert type="info">
                    <strong>Need more examples?</strong> Check out our GitHub repository for complete example applications
                    and integration guides for popular frameworks like React, Vue, and Express.js.
                  </Alert>
                </div>
              )}

              {/* Add placeholder content for other sections */}
              {activeSection === 'routing-strategies' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Routing Strategies</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      RouKey's intelligent routing strategies are the core of cost optimization and performance enhancement.
                      Each strategy uses advanced AI classification and analysis to route requests to the optimal model based on your specific requirements.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Strategy Overview</h2>
                    <div className="space-y-6">
                      <p className="text-gray-600 text-lg leading-relaxed">
                        RouKey offers seven distinct routing strategies, each designed for specific use cases and optimization goals.
                        All strategies include automatic failover, retry logic, and comprehensive analytics. The choice of strategy
                        depends on your priorities: cost optimization, reliability, performance, or specialized routing requirements.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Default Load Balancing</h3>
                          <p className="text-gray-600 text-sm">Automatic distribution with zero configuration</p>
                        </div>
                        <div className="p-4 bg-[#ff6b35]/10 rounded-xl border border-[#ff6b35]/20">
                          <h3 className="font-semibold text-gray-900 mb-2">Intelligent Role Routing</h3>
                          <p className="text-gray-600 text-sm">AI-powered role classification and routing</p>
                        </div>
                        <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Complexity-Based Routing</h3>
                          <p className="text-gray-600 text-sm">Cost optimization through complexity analysis</p>
                        </div>
                        <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Cost-Optimized Routing</h3>
                          <p className="text-gray-600 text-sm">Learning-based cost optimization</p>
                        </div>
                        <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                          <h3 className="font-semibold text-gray-900 mb-2">Strict Fallback</h3>
                          <p className="text-gray-600 text-sm">Predictable ordered failover sequence</p>
                        </div>
                        <div className="p-4 bg-indigo-50 rounded-xl border border-indigo-200">
                          <h3 className="font-semibold text-gray-900 mb-2">A/B Testing</h3>
                          <p className="text-gray-600 text-sm">Split traffic for performance comparison</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Default Load Balancing</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200">
                        <div className="flex items-center gap-3 mb-4">
                          <CogIcon className="h-6 w-6 text-blue-600" />
                          <h3 className="text-xl font-semibold text-gray-900">Zero Configuration Required</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          The default strategy requires no setup and automatically distributes requests across all active API keys
                          in your configuration. Perfect for getting started quickly or when you want simple, reliable load balancing.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">How It Works</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Round-Robin Distribution:</strong> Requests are distributed evenly across all available API keys</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Automatic Retry:</strong> Failed requests are automatically retried with different keys</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Health Monitoring:</strong> Unhealthy keys are temporarily removed from rotation</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Load Balancing:</strong> Prevents any single API key from being overwhelmed</span>
                            </li>
                          </ul>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Best Use Cases</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Getting started with RouKey</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Simple applications with consistent workloads</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>High-availability requirements</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>When you want zero configuration overhead</span>
                            </li>
                          </ul>
                        </div>
                      </div>

                      <Alert type="tip">
                        <strong>Performance:</strong> This strategy provides excellent reliability with 99.9% uptime and automatic failover.
                        While it doesn't optimize for cost like other strategies, it ensures consistent performance and is perfect for production workloads.
                      </Alert>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Intelligent Role Routing</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 p-6 rounded-xl border border-[#ff6b35]/20">
                        <div className="flex items-center gap-3 mb-4">
                          <BoltIcon className="h-6 w-6 text-[#ff6b35]" />
                          <h3 className="text-xl font-semibold text-gray-900">AI-Powered Request Classification</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          RouKey's most sophisticated routing strategy uses advanced AI classification to analyze each request and
                          determine the optimal model based on the detected role or task type. This enables specialized routing
                          for different types of work like coding, writing, analysis, and more.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Supported Roles</h3>
                          <div className="space-y-3">
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">general_chat</div>
                              <div className="text-sm text-gray-600">General conversation and Q&A</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">coding</div>
                              <div className="text-sm text-gray-600">Code generation, debugging, and review</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">writing</div>
                              <div className="text-sm text-gray-600">Content creation and editing</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">analysis</div>
                              <div className="text-sm text-gray-600">Data analysis and research</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="font-medium text-gray-900 mb-1">creative</div>
                              <div className="text-sm text-gray-600">Creative writing and brainstorming</div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">How Classification Works</h3>
                          <div className="space-y-4">
                            <div className="flex items-start gap-3">
                              <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0">
                                <span className="text-white font-bold text-sm">1</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Request Analysis</div>
                                <div className="text-sm text-gray-600">RouKey's AI classifier analyzes the user's prompt to understand intent and context</div>
                              </div>
                            </div>
                            <div className="flex items-start gap-3">
                              <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0">
                                <span className="text-white font-bold text-sm">2</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Role Detection</div>
                                <div className="text-sm text-gray-600">The system identifies the most appropriate role category for the request</div>
                              </div>
                            </div>
                            <div className="flex items-start gap-3">
                              <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0">
                                <span className="text-white font-bold text-sm">3</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Model Selection</div>
                                <div className="text-sm text-gray-600">Routes to the API key configured for that specific role</div>
                              </div>
                            </div>
                            <div className="flex items-start gap-3">
                              <div className="w-8 h-8 bg-[#ff6b35] rounded-lg flex items-center justify-center flex-shrink-0">
                                <span className="text-white font-bold text-sm">4</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Fallback Handling</div>
                                <div className="text-sm text-gray-600">If no specific role match, uses the default general chat model</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Multi-Role Detection & Orchestration</h3>
                        <p className="text-gray-600 mb-4">
                          For complex requests requiring multiple specialized capabilities, RouKey can detect multi-role requirements
                          and orchestrate workflows using our advanced LangGraph integration.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Sequential Workflow</div>
                            <div className="text-sm text-gray-600">Single roles, step-by-step</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Supervisor Workflow</div>
                            <div className="text-sm text-gray-600">2-3 roles, coordinated</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Hierarchical Workflow</div>
                            <div className="text-sm text-gray-600">4+ roles, complex tasks</div>
                          </div>
                        </div>
                      </div>

                      <Alert type="info">
                        <strong>Streaming Recommended:</strong> For multi-role tasks, enable streaming to avoid timeouts and get real-time progress updates.
                        RouKey automatically detects complex multi-role requirements and processes them asynchronously with streaming support.
                      </Alert>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Complexity-Based Routing</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
                        <div className="flex items-center gap-3 mb-4">
                          <CircleStackIcon className="h-6 w-6 text-green-600" />
                          <h3 className="text-xl font-semibold text-gray-900">Cost Optimization Through Intelligence</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          RouKey's complexity-based routing analyzes each prompt to determine its complexity level (1-5 scale) and
                          routes it to the most cost-effective model capable of handling that complexity. This strategy can reduce
                          costs by up to 60% while maintaining response quality.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Complexity Levels</h3>
                          <div className="space-y-3">
                            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">1</div>
                              <div>
                                <div className="font-medium text-gray-900">Simple</div>
                                <div className="text-sm text-gray-600">Basic Q&A, simple requests</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">2</div>
                              <div>
                                <div className="font-medium text-gray-900">Basic</div>
                                <div className="text-sm text-gray-600">Explanations, summaries</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                              <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">3</div>
                              <div>
                                <div className="font-medium text-gray-900">Moderate</div>
                                <div className="text-sm text-gray-600">Analysis, code review</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                              <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">4</div>
                              <div>
                                <div className="font-medium text-gray-900">Complex</div>
                                <div className="text-sm text-gray-600">Advanced reasoning, complex code</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                              <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">5</div>
                              <div>
                                <div className="font-medium text-gray-900">Expert</div>
                                <div className="text-sm text-gray-600">Research, advanced problem-solving</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Routing Logic</h3>
                          <div className="space-y-4">
                            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-2">Exact Match Routing</h4>
                              <p className="text-gray-600 text-sm">
                                RouKey first attempts to route to API keys specifically configured for the detected complexity level.
                              </p>
                            </div>
                            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-2">Proximal Search</h4>
                              <p className="text-gray-600 text-sm">
                                If no exact match is found, RouKey searches adjacent complexity levels (±1) to find suitable models.
                              </p>
                            </div>
                            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-2">Round-Robin Distribution</h4>
                              <p className="text-gray-600 text-sm">
                                Multiple keys at the same complexity level are used in round-robin fashion for load balancing.
                              </p>
                            </div>
                            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-2">Fallback Protection</h4>
                              <p className="text-gray-600 text-sm">
                                If no suitable complexity match is found, falls back to default general chat model.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Cost Optimization Example</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Before RouKey</h4>
                            <p className="text-gray-600 text-sm mb-3">All requests go to GPT-4 ($0.03/1K tokens)</p>
                            <ul className="text-gray-600 text-sm space-y-1">
                              <li>• Simple Q&A: $0.03/1K tokens</li>
                              <li>• Basic explanations: $0.03/1K tokens</li>
                              <li>• Complex analysis: $0.03/1K tokens</li>
                              <li>• <strong>Average cost: $0.03/1K tokens</strong></li>
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">With Complexity Routing</h4>
                            <p className="text-gray-600 text-sm mb-3">Intelligent model selection based on complexity</p>
                            <ul className="text-gray-600 text-sm space-y-1">
                              <li>• Simple Q&A: GPT-3.5 ($0.001/1K tokens)</li>
                              <li>• Basic explanations: GPT-3.5 ($0.001/1K tokens)</li>
                              <li>• Complex analysis: GPT-4 ($0.03/1K tokens)</li>
                              <li>• <strong>Average cost: $0.012/1K tokens (60% savings)</strong></li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <Alert type="tip">
                        <strong>Best Practice:</strong> Configure cheaper models (GPT-3.5, Claude Haiku) for complexity levels 1-2,
                        mid-tier models for level 3, and premium models (GPT-4, Claude Opus) for levels 4-5 to maximize cost savings.
                      </Alert>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Cost-Optimized Routing</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200">
                        <div className="flex items-center gap-3 mb-4">
                          <SparklesIcon className="h-6 w-6 text-purple-600" />
                          <h3 className="text-xl font-semibold text-gray-900">Learning-Based Optimization</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          RouKey's most advanced cost optimization strategy that learns from your usage patterns over time.
                          It combines complexity analysis with user behavior learning to provide personalized cost optimization
                          while maintaining response quality standards.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Learning Phases</h3>
                          <div className="space-y-4">
                            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                              <h4 className="font-medium text-gray-900 mb-2">Phase 1: Learning (First 50 requests)</h4>
                              <p className="text-gray-600 text-sm">
                                Conservative approach using complexity-based routing while collecting usage data and user feedback patterns.
                              </p>
                            </div>
                            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                              <h4 className="font-medium text-gray-900 mb-2">Phase 2: Optimization (51+ requests)</h4>
                              <p className="text-gray-600 text-sm">
                                Advanced routing using learned patterns, cost profiles, and quality preferences for maximum optimization.
                              </p>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Optimization Factors</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Usage Patterns:</strong> Analyzes your typical request types and complexity distribution</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Cost Sensitivity:</strong> Learns your cost vs. quality preferences from routing decisions</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Performance Metrics:</strong> Tracks response quality and user satisfaction indicators</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Model Performance:</strong> Monitors which models perform best for your specific use cases</span>
                            </li>
                          </ul>
                        </div>
                      </div>

                      <div className="bg-green-50 p-6 rounded-xl border border-green-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Advanced Features</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Adaptive Learning</div>
                            <div className="text-sm text-gray-600">Continuously improves routing decisions based on outcomes</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Quality Monitoring</div>
                            <div className="text-sm text-gray-600">Ensures cost optimization doesn't compromise response quality</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium text-gray-900 mb-1">Budget Awareness</div>
                            <div className="text-sm text-gray-600">Considers your budget constraints in routing decisions</div>
                          </div>
                        </div>
                      </div>

                      <Alert type="warning">
                        <strong>Learning Period:</strong> This strategy requires a learning period of 50+ requests to reach optimal performance.
                        During the learning phase, it uses conservative complexity-based routing while building your usage profile.
                      </Alert>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Strict Fallback Strategy</h2>
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 p-6 rounded-xl border border-yellow-200">
                        <div className="flex items-center gap-3 mb-4">
                          <ListBulletIcon className="h-6 w-6 text-yellow-600" />
                          <h3 className="text-xl font-semibold text-gray-900">Predictable Ordered Routing</h3>
                        </div>
                        <p className="text-gray-600 mb-4">
                          The strict fallback strategy provides maximum reliability and predictability by defining an ordered list
                          of API keys. RouKey will try them in sequence until one succeeds, making it perfect for mission-critical
                          applications where predictable behavior is essential.
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">How It Works</h3>
                          <div className="space-y-3">
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">1</div>
                              <div>
                                <div className="font-medium text-gray-900">Primary Model</div>
                                <div className="text-sm text-gray-600">First choice, highest priority</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">2</div>
                              <div>
                                <div className="font-medium text-gray-900">Secondary Model</div>
                                <div className="text-sm text-gray-600">Backup if primary fails</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                              <div className="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">3</div>
                              <div>
                                <div className="font-medium text-gray-900">Tertiary Model</div>
                                <div className="text-sm text-gray-600">Final fallback option</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                              <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">✗</div>
                              <div>
                                <div className="font-medium text-gray-900">Error Response</div>
                                <div className="text-sm text-gray-600">If all models fail</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Configuration Example</h3>
                          <CodeBlock title="Fallback order configuration" language="json">
{`{
  "routing_strategy": "strict_fallback",
  "routing_strategy_params": {
    "fallback_order": [
      "primary-gpt4-key",
      "backup-claude-key",
      "emergency-gpt35-key"
    ],
    "retry_attempts": 3,
    "timeout_seconds": 30
  }
}`}
                          </CodeBlock>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Best Use Cases</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Mission-critical applications requiring guaranteed responses</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Applications with strict SLA requirements</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>When you need predictable routing behavior</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <CheckIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>Compliance requirements for specific model usage</span>
                            </li>
                          </ul>
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">Advanced Features</h3>
                          <ul className="space-y-3 text-gray-600">
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Health Monitoring:</strong> Automatically skips unhealthy models in the chain</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Retry Logic:</strong> Configurable retry attempts for each model in the chain</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Timeout Control:</strong> Individual timeout settings for each fallback level</span>
                            </li>
                            <li className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></div>
                              <span><strong>Detailed Logging:</strong> Complete audit trail of fallback decisions</span>
                            </li>
                          </ul>
                        </div>
                      </div>

                      <Alert type="tip">
                        <strong>Reliability:</strong> This strategy provides the highest reliability with guaranteed fallback behavior.
                        It's ideal for production environments where consistent availability is more important than cost optimization.
                      </Alert>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'configuration' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Configuration</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Learn how to configure RouKey for optimal performance.
                    </p>
                  </div>
                  <div className="card p-8 text-center">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Coming Soon</h3>
                    <p className="text-gray-600">Configuration documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'sdks' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">SDKs & Libraries</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Official SDKs and community libraries for RouKey.
                    </p>
                  </div>
                  <div className="card p-8 text-center">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Coming Soon</h3>
                    <p className="text-gray-600">SDK documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'limits' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">Limits & Pricing</h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Understanding RouKey's usage limits and pricing structure.
                    </p>
                  </div>
                  <div className="card p-8 text-center">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Coming Soon</h3>
                    <p className="text-gray-600">Limits and pricing documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'future-releases' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">
                      Future Releases
                    </h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Discover what's coming next in RouKey's roadmap and planned features for upcoming releases.
                    </p>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Q1 2025 - Enhanced Workflow Automation</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <RocketLaunchIcon className="h-5 w-5 text-blue-600" />
                          Advanced Workflow Engine
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Full n8n-style workflow automation with visual workflow builder and advanced task orchestration.
                        </p>
                        <div className="text-sm text-blue-600 font-medium">Status: In Development</div>
                      </div>
                      <div className="p-6 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl border border-green-200">
                        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                          <SparklesIcon className="h-5 w-5 text-green-600" />
                          Enhanced Memory System
                        </h3>
                        <p className="text-gray-600 mb-4">
                          Persistent memory across workflow executions with intelligent context management and sub-task tracking.
                        </p>
                        <div className="text-sm text-green-600 font-medium">Status: Design Phase</div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Q2 2025 - Performance & Scale</h2>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Ultra-Low Latency</h3>
                          <p className="text-gray-600 mb-4">Target latency reduction to 50-100ms range for lightning-fast responses.</p>
                          <div className="text-sm text-gray-500">Current: 100-500ms</div>
                        </div>
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Global Edge Network</h3>
                          <p className="text-gray-600 mb-4">Worldwide edge deployment for reduced latency and improved reliability.</p>
                          <div className="text-sm text-gray-500">Planned: 15+ regions</div>
                        </div>
                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Advanced Caching</h3>
                          <p className="text-gray-600 mb-4">Multi-layer caching with predictive pre-loading and intelligent invalidation.</p>
                          <div className="text-sm text-gray-500">Target: 90% cache hit rate</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Q3 2025 - AI-Powered Features</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Intelligent Cost Prediction</h3>
                        <p className="text-gray-600 mb-4">
                          AI-powered cost forecasting and budget optimization with predictive analytics.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Monthly cost predictions</li>
                          <li>• Usage pattern analysis</li>
                          <li>• Automatic budget alerts</li>
                          <li>• Optimization recommendations</li>
                        </ul>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Auto-Scaling Routing</h3>
                        <p className="text-gray-600 mb-4">
                          Dynamic routing strategies that adapt to traffic patterns and model performance in real-time.
                        </p>
                        <ul className="space-y-2 text-gray-600">
                          <li>• Traffic-based scaling</li>
                          <li>• Performance monitoring</li>
                          <li>• Automatic strategy switching</li>
                          <li>• Load balancing optimization</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="card p-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Q4 2025 - Enterprise Features</h2>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200">
                          <h3 className="font-semibold text-gray-900 mb-3">Advanced Analytics Dashboard</h3>
                          <p className="text-gray-600 mb-4">
                            Comprehensive analytics with custom reporting, cost breakdowns, and performance insights.
                          </p>
                          <ul className="space-y-1 text-gray-600 text-sm">
                            <li>• Custom dashboards</li>
                            <li>• Real-time monitoring</li>
                            <li>• Cost attribution</li>
                            <li>• Performance metrics</li>
                          </ul>
                        </div>
                        <div className="p-6 bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl border border-[#ff6b35]/20">
                          <h3 className="font-semibold text-gray-900 mb-3">Enterprise Security</h3>
                          <p className="text-gray-600 mb-4">
                            Advanced security features including SSO, audit logs, and compliance certifications.
                          </p>
                          <ul className="space-y-1 text-gray-600 text-sm">
                            <li>• Single Sign-On (SSO)</li>
                            <li>• Audit logging</li>
                            <li>• SOC 2 compliance</li>
                            <li>• Role-based access control</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white">
                    <h3 className="text-xl font-semibold mb-4">Want to influence our roadmap?</h3>
                    <p className="text-white/90 mb-6">
                      We value feedback from our community. Share your feature requests and help shape RouKey's future.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <a
                        href="mailto:<EMAIL>"
                        className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors text-center"
                      >
                        Submit Feature Request
                      </a>
                      <a
                        href="https://github.com/DRIM-ai/RouKey"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors text-center"
                      >
                        View on GitHub
                      </a>
                    </div>
                  </div>
                </div>
              )}

              {activeSection === 'faq' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">
                      Frequently Asked Questions
                    </h1>
                    <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                      Find answers to common questions about RouKey's features, pricing, and implementation.
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">How does RouKey reduce API costs?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        RouKey reduces costs through intelligent routing strategies that automatically select the most cost-effective model for each request.
                        Our complexity-based routing can route simple tasks to cheaper models (like GPT-3.5) while reserving premium models (like GPT-4) for complex tasks.
                        Additionally, our semantic caching system prevents duplicate API calls for similar requests, further reducing costs.
                      </p>
                    </div>

                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Is RouKey compatible with existing OpenAI code?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Yes! RouKey is fully compatible with OpenAI's API. You can use existing OpenAI SDKs and simply change the base URL to RouKey's endpoint.
                        All OpenAI parameters are supported, plus RouKey adds additional parameters like "role" for enhanced routing capabilities.
                      </p>
                    </div>

                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">What happens if a model is unavailable?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        RouKey includes automatic failover mechanisms. If your primary model is unavailable, RouKey will automatically route to backup models
                        based on your configured strategy. Our strict fallback strategy provides guaranteed failover chains, while other strategies include
                        intelligent fallback logic to ensure high availability.
                      </p>
                    </div>

                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">How secure are my API keys?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Your API keys are encrypted using enterprise-grade AES-256 encryption and stored securely in our database.
                        RouKey follows a BYOK (Bring Your Own Keys) model, meaning you maintain control of your API keys.
                        We never store or log your actual API responses, and all communications are encrypted in transit.
                      </p>
                    </div>

                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Can I use RouKey for production applications?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Absolutely! RouKey is designed for production use with enterprise-grade reliability, security, and performance.
                        We offer 99.9% uptime SLA, comprehensive monitoring, and 24/7 support for professional and enterprise plans.
                        Many companies use RouKey to handle millions of requests per month.
                      </p>
                    </div>

                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">What's the difference between routing strategies?</h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        RouKey offers several routing strategies for different use cases:
                      </p>
                      <ul className="space-y-2 text-gray-600 ml-4">
                        <li>• <strong>Intelligent Role:</strong> AI classifies requests by role (coding, writing, etc.) and routes accordingly</li>
                        <li>• <strong>Complexity-Based:</strong> Analyzes prompt complexity and routes to appropriate models for cost optimization</li>
                        <li>• <strong>Strict Fallback:</strong> Ordered failover sequence for maximum reliability</li>
                        <li>• <strong>Cost-Optimized:</strong> Learns your usage patterns and optimizes for cost while maintaining quality</li>
                      </ul>
                    </div>

                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Do you support streaming responses?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Yes! RouKey fully supports streaming responses and we actually recommend using streaming for complex multi-role tasks.
                        Streaming helps avoid timeout issues on platforms like Vercel and provides a better user experience with real-time responses.
                        Our multi-role orchestration system works seamlessly with streaming.
                      </p>
                    </div>

                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">How does the free tier work?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        The free tier includes unlimited API requests, 1 custom configuration, up to 3 API keys, access to all 300+ models,
                        and strict fallback routing. However, it doesn't include advanced routing strategies, custom roles, or premium features.
                        It's perfect for testing RouKey and small projects.
                      </p>
                    </div>

                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Can I cancel my subscription anytime?</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Yes, you can cancel your subscription at any time. Your service will continue until the end of your current billing period,
                        and you can always reactivate later. We also offer a 14-day money-back guarantee for new subscribers.
                      </p>
                    </div>

                    <div className="card p-8">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">How do I get support?</h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        We offer multiple support channels:
                      </p>
                      <ul className="space-y-2 text-gray-600 ml-4">
                        <li>• <strong>Email:</strong> <EMAIL> for general inquiries</li>
                        <li>• <strong>Technical:</strong> <EMAIL> for technical support</li>
                        <li>• <strong>GitHub:</strong> Issues and feature requests on our repository</li>
                        <li>• <strong>Documentation:</strong> Comprehensive guides and examples</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-8 rounded-xl text-white">
                    <h3 className="text-xl font-semibold mb-4">Still have questions?</h3>
                    <p className="text-white/90 mb-6">
                      Can't find what you're looking for? Our team is here to help with any questions about RouKey.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <a
                        href="mailto:<EMAIL>"
                        className="bg-white text-[#ff6b35] px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors text-center"
                      >
                        Contact Support
                      </a>
                      <a
                        href="https://github.com/DRIM-ai/RouKey"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/30 transition-colors text-center"
                      >
                        GitHub Issues
                      </a>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
