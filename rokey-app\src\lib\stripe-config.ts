// Stripe Configuration with Environment Detection
// Automatically switches between test and live keys based on environment

const isProduction = process.env.NODE_ENV === 'production';

// Stripe Keys - Auto-selected based on environment
export const STRIPE_KEYS = {
  publishableKey: isProduction 
    ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY!
    : process.env.STRIPE_TEST_PUBLISHABLE_KEY!,
  
  secretKey: isProduction 
    ? process.env.STRIPE_LIVE_SECRET_KEY!
    : process.env.STRIPE_TEST_SECRET_KEY!,
    
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!
};

// Stripe Price IDs - Auto-selected based on environment
export const STRIPE_PRICE_IDS = {
  FREE: isProduction 
    ? process.env.STRIPE_LIVE_FREE_PRICE_ID!
    : process.env.STRIPE_TEST_FREE_PRICE_ID!,
    
  STARTER: isProduction 
    ? process.env.STRIPE_LIVE_STARTER_PRICE_ID!
    : process.env.STRIPE_TEST_STARTER_PRICE_ID!,
    
  PROFESSIONAL: isProduction 
    ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID!
    : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID!,
    
  ENTERPRISE: isProduction 
    ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID!
    : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID!
};

// Stripe Product IDs - Auto-selected based on environment
export const STRIPE_PRODUCT_IDS = {
  FREE: isProduction 
    ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID!
    : process.env.STRIPE_TEST_FREE_PRODUCT_ID!,
    
  STARTER: isProduction 
    ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID!
    : process.env.STRIPE_TEST_STARTER_PRODUCT_ID!,
    
  PROFESSIONAL: isProduction 
    ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID!
    : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID!,
    
  ENTERPRISE: isProduction 
    ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID!
    : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID!
};

// Public Price IDs for frontend (auto-selected based on environment)
export const PUBLIC_STRIPE_PRICE_IDS = {
  FREE: isProduction 
    ? process.env.STRIPE_LIVE_FREE_PRICE_ID!
    : process.env.STRIPE_TEST_FREE_PRICE_ID!,
    
  STARTER: isProduction 
    ? process.env.STRIPE_LIVE_STARTER_PRICE_ID!
    : process.env.STRIPE_TEST_STARTER_PRICE_ID!,
    
  PROFESSIONAL: isProduction 
    ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID!
    : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID!,
    
  ENTERPRISE: isProduction 
    ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID!
    : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID!
};

// Environment info for debugging
export const STRIPE_ENV_INFO = {
  isProduction,
  environment: isProduction ? 'LIVE' : 'TEST',
  keysUsed: {
    publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',
    secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined'
  }
};

// Log environment info in development
if (!isProduction) {
  console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);
  console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);
}
